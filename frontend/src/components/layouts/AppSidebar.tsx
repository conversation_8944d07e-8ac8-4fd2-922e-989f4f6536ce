import React from "react"
import Image from "next/image"
import Link from "next/link"

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
} from "@/components/ui/sidebar"
import NavAnalyses from "@/components/layouts/NavAnalyses"
import NavMain from "@/components/layouts/NavMain"
import NavRV from "@/components/layouts/NavRV"

const AppSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => (
  <Sidebar {...props}>
    <SidebarHeader>
      <SidebarMenu className="items-start p-2 pb-0">
        <Link href="/">
          <Image
            className="flex h-7 w-auto object-contain dark:hidden"
            src="/logo-blue.png"
            alt="Singapore Medical Group"
            width={512}
            height={175}
            priority
          />
          <Image
            className="hidden h-7 w-auto object-contain dark:flex"
            src="/logo-white.png"
            alt="Singapore Medical Group"
            width={512}
            height={175}
            priority
          />
        </Link>
      </SidebarMenu>
    </SidebarHeader>

    <SidebarContent className="overflow-x-hidden">
      <NavMain />
      <NavAnalyses />
      <NavRV />
    </SidebarContent>

    <SidebarRail />
  </Sidebar>
)

export default AppSidebar
