"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Banknote,
  Building,
  ChartColumn,
  ChevronRight,
  CircleHelp,
  Database,
  // FileMinus,
  LayoutDashboard,
  Settings,
  Store,
  // TriangleAlert,
} from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"

const PAGES = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Marketing",
    url: "/marketing",
    icon: Store,
    subPages: [
      {
        url: "/funnel-analysis",
        title: "Funnel Analysis",
      },
      {
        url: "/campaign-performance",
        title: "Campaign Performance",
      },
    ],
  },
  {
    title: "Financial",
    url: "/financial-rv",
    icon: Banknote,
    subPages: [
      {
        url: "/revenue-vs-budget",
        title: "Revenue vs. Budget",
      },
      {
        url: "/expense-analysis",
        title: "Expense Analysis",
      },
    ],
  },
  {
    title: "Operations",
    url: "/operations-rv",
    icon: Building,
    subPages: [
      {
        url: "/clinic-utilization",
        title: "Clinic Utilization",
      },
      {
        url: "/appointment-metrics",
        title: "Appointment Metrics",
      },
      {
        url: "/staff-scheduling",
        title: "Staff Scheduling",
      },
    ],
  },
  {
    title: "KPIs",
    url: "/kpi",
    icon: Building,
  },
  {
    title: "Analysis",
    url: "/analysis",
    icon: ChartColumn,
    subPages: [
      {
        url: "/retention-curves",
        title: "Retention Curves",
      },
      {
        url: "/clv-distribution",
        title: "CLV Distribution",
      },
    ],
  },
  // {
  //   title: "Alerts & Actions",
  //   url: "/alerts",
  //   icon: TriangleAlert,
  //   subPages: [
  //     {
  //       url: "/open-alerts",
  //       title: "Open Alerts",
  //     },
  //     {
  //       url: "/prescriptive-tasks",
  //       title: "Prescriptive Tasks",
  //     },
  //   ],
  // },
  {
    title: "Database",
    url: "/database",
    icon: Database,
    subPages: [
      {
        url: "/etl-status",
        title: "ETL Status",
      },
      {
        url: "/data-quality",
        title: "Data-Quality Logs",
      },
    ],
  },
  // {
  //   title: "Reports",
  //   url: "/reports",
  //   icon: FileMinus,
  //   subPages: [
  //     {
  //       url: "/export",
  //       title: "Export CSV / PDF",
  //     },
  //     {
  //       url: "/scheduled",
  //       title: "Scheduled Reports",
  //     },
  //   ],
  // },
  {
    title: "Settings",
    url: "/settings-rv",
    icon: Settings,
    subPages: [
      {
        url: "/data-sources",
        title: "Data Sources",
      },
      {
        url: "/users",
        title: "Users & Roles",
      },
      {
        url: "/notifications",
        title: "Notification Preferences",
      },
    ],
  },
  {
    title: "Help",
    url: "/help",
    icon: CircleHelp,
    subPages: [
      {
        url: "/documentation",
        title: "Documentation",
      },
      {
        url: "/support",
        title: "Support",
      },
    ],
  },
]

const NavRV = () => {
  const pathname = usePathname()
  const { setOpenMobile } = useSidebar()

  return (
    <Collapsible className="group/collapsible" defaultOpen={false}>
      <SidebarGroup>
        <SidebarGroupLabel asChild>
          <CollapsibleTrigger>
            Application (RV)
            <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
          </CollapsibleTrigger>
        </SidebarGroupLabel>

        <CollapsibleContent>
          <SidebarGroupContent>
            <SidebarMenu>
              {PAGES.map((page) => (
                <Collapsible
                  key={page.title}
                  defaultOpen={pathname === page.url}
                  onClick={() => setOpenMobile(false)}
                  asChild
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      isActive={pathname === page.url}
                      onClick={() => setOpenMobile(false)}
                      asChild
                    >
                      <Link href={page.url}>{page.title}</Link>
                    </SidebarMenuButton>

                    {page.subPages && page.subPages.length !== 0 && (
                      <>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuAction className="data-[state=open]:rotate-90">
                            <ChevronRight />
                          </SidebarMenuAction>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {page.subPages.map((subPage) => (
                              <SidebarMenuSubItem key={subPage.title}>
                                <SidebarMenuSubButton
                                  isActive={
                                    pathname === `${page.url}${subPage.url}`
                                  }
                                  onClick={() => setOpenMobile(false)}
                                  asChild
                                >
                                  <Link href={`${page.url}${subPage.url}`}>
                                    {subPage.title}
                                  </Link>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </>
                    )}
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </CollapsibleContent>
      </SidebarGroup>
    </Collapsible>
  )
}

export default NavRV
