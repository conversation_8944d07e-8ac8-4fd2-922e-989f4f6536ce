import { NextRequest, NextResponse } from "next/server"

import { Chart } from "@/app/(main)/deep-dive/components/types"

// Mock database for charts
// In a real implementation, this would be replaced with a database connection
const chartDatabase: Record<string, Chart[]> = {}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const analysisType = searchParams.get("analysisType")

  if (!analysisType) {
    return NextResponse.json(
      { error: "Analysis type is required" },
      { status: 400 }
    )
  }

  // Return charts for the specified analysis type
  return NextResponse.json({
    charts: chartDatabase[analysisType] || [],
  })
}

export async function POST(request: NextRequest) {
  const data = await request.json()
  const { analysisType, charts } = data

  if (!analysisType || !charts) {
    return NextResponse.json(
      { error: "Analysis type and charts are required" },
      { status: 400 }
    )
  }

  // Save charts for the specified analysis type
  chartDatabase[analysisType] = charts

  return NextResponse.json({
    success: true,
    message: `Saved ${charts.length} charts for ${analysisType} analysis`,
    charts: chartDatabase[analysisType],
  })
}

export async function PUT(request: NextRequest) {
  const data = await request.json()
  const { analysisType, chart } = data

  if (!analysisType || !chart) {
    return NextResponse.json(
      { error: "Analysis type and chart are required" },
      { status: 400 }
    )
  }

  // Initialize the array if it doesn't exist
  if (!chartDatabase[analysisType]) {
    chartDatabase[analysisType] = []
  }

  // Find and update the chart if it exists, otherwise add it
  const index = chartDatabase[analysisType].findIndex((c) => c.id === chart.id)

  if (index !== -1) {
    chartDatabase[analysisType][index] = chart
  } else {
    chartDatabase[analysisType].push(chart)
  }

  return NextResponse.json({
    success: true,
    message: `Updated chart ${chart.id} for ${analysisType} analysis`,
    chart,
  })
}

export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const analysisType = searchParams.get("analysisType")
  const chartId = searchParams.get("chartId")

  if (!analysisType || !chartId) {
    return NextResponse.json(
      { error: "Analysis type and chart ID are required" },
      { status: 400 }
    )
  }

  // Remove the chart if it exists
  if (chartDatabase[analysisType]) {
    chartDatabase[analysisType] = chartDatabase[analysisType].filter(
      (c) => c.id !== chartId
    )

    // Update order for remaining charts
    chartDatabase[analysisType] = chartDatabase[analysisType].map(
      (chart, index) => ({
        ...chart,
        order: index + 1,
      })
    )
  }

  return NextResponse.json({
    success: true,
    message: `Deleted chart ${chartId} from ${analysisType} analysis`,
  })
}
