import React from 'react';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Cell,
  LabelList,
  Legend
} from 'recharts';

// Sample data for product line performance
const productData = [
  { 
    name: 'Product Line A', 
    margin: 42, 
    previousYear: 38.5, 
    change: 3.5,
    revenue: 12500000
  },
  { 
    name: 'Product Line B', 
    margin: 36.2, 
    previousYear: 35, 
    change: 1.2,
    revenue: 8750000
  },
  { 
    name: 'Product Line C', 
    margin: 31.8, 
    previousYear: 31, 
    change: 0.8,
    revenue: 6250000
  }
];

const ProductPerformanceChart: React.FC = () => {
  // Format numbers for tooltip
  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };
  
  const formatCurrency = (value: number) => {
    return `$${(value / 1000000).toFixed(1)}M`;
  };
  
  // Custom tooltip to show all relevant metrics
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-sm rounded-md">
          <p className="font-medium text-sm text-gray-800">{label}</p>
          <div className="mt-2 space-y-1">
            <p className="text-xs">
              <span className="inline-block w-32 text-gray-800">Current Margin:</span> 
              <span className="font-medium text-black">{formatPercent(payload[0].value)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-32 text-gray-800">Previous Year:</span> 
              <span className="font-medium text-black">{formatPercent(payload[0].payload.previousYear)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-32 text-gray-800">Change:</span> 
              <span className="font-medium text-[#2EC4B6]">+{formatPercent(payload[0].payload.change)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-32 text-gray-800">Annual Revenue:</span> 
              <span className="font-medium text-black">{formatCurrency(payload[0].payload.revenue)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Use the app's color scheme
  const getBarColor = (index: number) => {
    // Use navy for first bar, turquoise for others
    return index === 0 ? '#0A2342' : '#2EC4B6';
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart
        data={productData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        barSize={60}
      >
        <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
        <XAxis 
          dataKey="name" 
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
        />
        <YAxis 
          tickFormatter={formatPercent}
          domain={[0, 50]}
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
          width={45}
          label={{ 
            value: 'Contribution Margin %', 
            angle: -90, 
            position: 'insideLeft',
            style: { textAnchor: 'middle', fontSize: 12, fill: '#6B7280' }
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          formatter={(value) => <span className="text-xs font-medium">{value}</span>}
          wrapperStyle={{ paddingTop: 10 }}
        />
        <Bar 
          dataKey="margin" 
          name="Contribution Margin" 
          radius={[4, 4, 0, 0]}
        >
          {productData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(index)} />
          ))}
          <LabelList 
            dataKey="margin" 
            position="top" 
            formatter={formatPercent} 
            style={{ fontSize: 11, fill: '#4B5563', fontWeight: 500 }}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default ProductPerformanceChart;
