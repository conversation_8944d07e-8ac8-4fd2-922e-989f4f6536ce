import { API_CONFIG } from "./environment"
import {
  analysisViews,
  initialDataSources,
  initialTableData,
  mockAnalysisData,
} from "./mockData"
import {
  AnalysisData,
  AnalysisView,
  DataSource,
  ExtendedRowData,
} from "./types"

/**
 * Helper function to simulate API delay
 */
const simulateApiDelay = <T>(data: T): Promise<T> => {
  return new Promise((resolve) =>
    setTimeout(() => resolve(data), API_CONFIG.MOCK_DELAY)
  )
}

/**
 * Mock service for handling mock data
 */
class MockService {
  /**
   * Get mock analysis views
   */
  async getAnalysisViews(): Promise<AnalysisView[]> {
    return simulateApiDelay(analysisViews)
  }

  /**
   * Get mock analysis data for a specific view
   */
  async getAnalysisData(viewId: number): Promise<AnalysisData> {
    // In a more sophisticated mock service, we could filter by viewId
    return simulateApiDelay(mockAnalysisData)
  }

  /**
   * Get mock table data
   */
  async getTableData(): Promise<ExtendedRowData[]> {
    return simulateApiDelay(initialTableData)
  }

  /**
   * Get mock data sources
   */
  async getDataSources(): Promise<DataSource[]> {
    return simulateApiDelay(initialDataSources)
  }

  /**
   * Save a new analysis (mock)
   */
  async saveAnalysis(
    parentId: number,
    name: string,
    filters: any,
    measures: any
  ): Promise<AnalysisView> {
    const savedAnalysis: AnalysisView = {
      id: Math.floor(Math.random() * 1000) + 100, // Generate a random ID
      name: name,
      parentId: parentId,
      health: "green",
      children: [],
    }

    return simulateApiDelay(savedAnalysis)
  }

  /**
   * Update an existing analysis (mock)
   */
  async updateAnalysis(
    viewId: number,
    filters: any,
    measures: any
  ): Promise<AnalysisData> {
    return simulateApiDelay(mockAnalysisData)
  }

  /**
   * Toggle favorite status for an analysis view (mock)
   */
  async toggleFavorite(
    viewId: number
  ): Promise<{ id: number; favorite: boolean; success: boolean }> {
    // Find the view in our mock data to toggle its favorite status
    let targetView: AnalysisView | undefined

    // Search through all parent views and their children
    for (const view of analysisViews) {
      if (view.id === viewId) {
        targetView = view
        break
      }

      if (view.children) {
        const childView = view.children.find((child) => child.id === viewId)
        if (childView) {
          targetView = childView
          break
        }
      }
    }

    if (targetView) {
      targetView.favorite = !targetView.favorite
      const result = {
        id: targetView.id,
        favorite: targetView.favorite,
        success: true,
      }
      return simulateApiDelay(result)
    }

    return simulateApiDelay({ id: viewId, favorite: false, success: false })
  }

  /**
   * Edit an analysis view (mock)
   */
  async editAnalysisView(
    viewId: number,
    data: { name?: string; description?: string }
  ): Promise<{ id: number; success: boolean }> {
    // Find the view in our mock data to update it
    let targetView: AnalysisView | undefined

    // Search through all parent views and their children
    for (const view of analysisViews) {
      if (view.id === viewId) {
        targetView = view
        break
      }

      if (view.children) {
        const childView = view.children.find((child) => child.id === viewId)
        if (childView) {
          targetView = childView
          break
        }
      }
    }

    if (targetView && data.name) {
      targetView.name = data.name
      const result = {
        id: targetView.id,
        success: true,
      }
      return simulateApiDelay(result)
    }

    return simulateApiDelay({ id: viewId, success: false })
  }

  /**
   * Delete an analysis view (mock)
   */
  async deleteAnalysisView(
    viewId: number
  ): Promise<{ success: boolean; message?: string }> {
    // Find and remove the view from our mock data
    let deleted = false

    // Search through parent views
    for (let i = 0; i < analysisViews.length; i++) {
      if (analysisViews[i].id === viewId) {
        analysisViews.splice(i, 1)
        deleted = true
        break
      }

      // Search through children
      if (analysisViews[i].children) {
        const children = analysisViews[i].children
        if (children) {
          const childIndex = children.findIndex((child) => child.id === viewId)
          if (childIndex !== -1) {
            children.splice(childIndex, 1)
            deleted = true
            break
          }
        }
      }
    }

    if (deleted) {
      const result = {
        success: true,
        message: "Analysis view has been deleted",
      }
      return simulateApiDelay(result)
    }

    return simulateApiDelay({
      success: false,
      message: "Failed to delete analysis view",
    })
  }
}

// Create and export a singleton instance
export const mockService = new MockService()
