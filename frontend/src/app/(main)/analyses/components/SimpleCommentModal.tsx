import React, { useState } from "react"

interface SimpleCommentModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (comment: string) => void
  selectedText: string
  mode: "add" | "request"
  teamMembers?: { id: string; name: string }[]
}

const SimpleCommentModal: React.FC<SimpleCommentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  selectedText,
  mode,
  teamMembers = [],
}) => {
  const [comment, setComment] = useState("")
  const [selectedTeamMembers, setSelectedTeamMembers] = useState<string[]>([])

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (mode === "add" && comment.trim()) {
      onSave(comment)
    } else if (mode === "request" && selectedTeamMembers.length > 0) {
      onSave(JSON.stringify({ teamMembers: selectedTeamMembers }))
    }
  }

  const toggleTeamMember = (id: string) => {
    setSelectedTeamMembers((prev) =>
      prev.includes(id)
        ? prev.filter((memberId) => memberId !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className="bg-primary z-50 mx-auto w-full max-w-md overflow-hidden rounded-lg shadow-lg">
        <div className="p-6">
          <h3 className="mb-4 text-xl font-semibold text-white">
            {mode === "add" ? "Add Comment" : "Request Comment"}
          </h3>

          <div className="bg-primary mb-4 rounded border border-gray-600 p-3">
            <p className="text-sm text-gray-300">"{selectedText}"</p>
          </div>

          <form onSubmit={handleSubmit}>
            {mode === "add" ? (
              <div className="mb-4">
                <label
                  htmlFor="comment"
                  className="mb-2 block text-sm font-medium text-gray-300"
                >
                  Your Comment
                </label>
                <textarea
                  id="comment"
                  className="bg-primary focus:ring-primary w-full rounded border border-gray-600 p-2 text-white focus:ring-2 focus:outline-none"
                  rows={4}
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  placeholder="Add your comment..."
                  autoFocus
                />
              </div>
            ) : (
              <div className="mb-4">
                <label className="mb-2 block text-sm font-medium text-gray-300">
                  Select Team Members
                </label>
                <div className="bg-primary max-h-48 overflow-y-auto rounded border border-gray-600 p-3">
                  {teamMembers.map((member) => (
                    <div key={member.id} className="mb-2 flex items-center">
                      <input
                        type="checkbox"
                        id={`member-${member.id}`}
                        checked={selectedTeamMembers.includes(member.id)}
                        onChange={() => toggleTeamMember(member.id)}
                        className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-500"
                      />
                      <label
                        htmlFor={`member-${member.id}`}
                        className="ml-2 text-sm text-gray-300"
                      >
                        {member.name}
                      </label>
                    </div>
                  ))}
                  {teamMembers.length === 0 && (
                    <p className="text-sm text-gray-400">
                      No team members available
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="bg-primary hover:bg-primary rounded border border-gray-600 px-4 py-2 text-gray-300"
                onClick={onClose}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={
                  (mode === "add" && !comment.trim()) ||
                  (mode === "request" && selectedTeamMembers.length === 0)
                }
                className="bg-primary hover:bg-primary rounded px-4 py-2 text-white disabled:cursor-not-allowed disabled:opacity-50"
              >
                {mode === "add" ? "Save Comment" : "Request Comment"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default SimpleCommentModal
