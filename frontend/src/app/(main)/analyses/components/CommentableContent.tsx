import React, { useEffect, useRef, useState } from "react"

import CommentContextMenu from "./CommentContextMenu"
import SimpleCommentModal from "./SimpleCommentModal"
import SimpleCommentViewer from "./SimpleCommentViewer"
import useComments from "./useComments"

interface CommentableContentProps {
  content: string
  sectionId: string
  className?: string
}

const CommentableContent: React.FC<CommentableContentProps> = ({
  content,
  sectionId,
  className = "",
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const {
    comments,
    selectedText,
    contextMenuPosition,
    isCommentModalOpen,
    commentModalMode,
    activeCommentId,
    teamMembers,
    closeContextMenu,
    openCommentModal,
    closeCommentModal,
    addComment,
    viewComments,
    closeCommentViewer,
    getActiveComments,
    addResponseToComment,
  } = useComments(sectionId)

  // We're not setting up any selection event listeners here
  // The selection is handled entirely by the browser's native selection mechanism
  // and we only capture it when the context menu is opened

  // Render comment markers
  const [renderedContent, setRenderedContent] = useState(content)

  useEffect(() => {
    if (comments.length === 0) {
      setRenderedContent(content)
      return
    }

    // Create a temporary div to parse the HTML content
    const tempDiv = document.createElement("div")
    tempDiv.innerHTML = content

    // Get the text content
    const textContent = tempDiv.textContent || ""

    // Create a new HTML string with comment markers
    let newContent = content

    // Sort comments in reverse order (to avoid index issues when replacing)
    const sortedComments = [...comments].sort(
      (a, b) => b.selectionStart - a.selectionStart
    )

    for (const commentData of sortedComments) {
      try {
        // Find the comment text in the content
        const commentText = commentData.textContent
        const commentIndex = textContent.indexOf(
          commentText,
          commentData.selectionStart
        )

        if (commentIndex >= 0) {
          // Create a placeholder to find in the HTML
          const placeholder = commentText

          // Find the end of the line containing the comment text
          // We'll need to analyze the HTML to find line breaks
          const tempDiv = document.createElement("div")
          tempDiv.innerHTML = content

          // Function to find the end of line position
          const findEndOfLine = (html: string, startPos: number): number => {
            // Look for the next <br>, </p>, or </div> after the comment text
            const brPos = html.indexOf("<br", startPos)
            const pEndPos = html.indexOf("</p>", startPos)
            const divEndPos = html.indexOf("</div>", startPos)

            // Find the closest end marker
            const positions = [brPos, pEndPos, divEndPos].filter(
              (pos) => pos !== -1
            )
            if (positions.length === 0) return html.length // If no end markers, use end of content

            return Math.min(...positions)
          }

          // Find the position of the comment text in the content
          const commentStartPos = content.indexOf(commentText)
          if (commentStartPos === -1) continue // Skip this comment if text not found

          // Find the end of the line containing the comment
          const lineEndPos = findEndOfLine(
            content,
            commentStartPos + commentText.length
          )

          // Create the comment icon HTML
          const commentIconHtml = `<span class="inline-block" data-comment-id="${commentData.id}">
            <span class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-primary hover:bg-primary transition-colors ml-1 relative cursor-pointer">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="h-3 w-3">
                <path fill-rule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-7.152.52c-2.43 0-4.817-.178-7.152-.52C2.87 16.438 1.5 14.706 1.5 12.76V6.741c0-1.946 1.37-3.68 3.348-3.97z" clip-rule="evenodd"></path>
              </svg>
              ${commentData.comments.length > 0 ? `<span class="absolute -top-2 -right-2 flex items-center justify-center h-4 w-4 text-xs font-bold text-white bg-red-500 rounded-full">${commentData.comments.length > 9 ? "9+" : commentData.comments.length}</span>` : ""}
            </span>
          </span>`

          // Create the replacement HTML with comment marker at the end of the line
          const replacement = `<span class="border-b-2 border-primary">${commentText}</span>`

          // Insert the comment icon at the end of the line
          newContent =
            content.substring(0, commentStartPos) +
            replacement +
            content.substring(
              commentStartPos + commentText.length,
              lineEndPos
            ) +
            commentIconHtml +
            content.substring(lineEndPos)

          // Break after processing the first comment as we've modified the content
          break
        }
      } catch (error) {
        console.error("Error rendering comment marker:", error)
      }
    }

    setRenderedContent(newContent)
  }, [comments, content])

  // Handle clicks on comment icons
  useEffect(() => {
    const handleCommentIconClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      const iconContainer = target.closest("[data-comment-id]")

      if (iconContainer) {
        e.preventDefault()
        e.stopPropagation()

        const commentId = (iconContainer as HTMLElement).dataset.commentId
        if (commentId) {
          viewComments(commentId)
        }
      }
    }

    const contentElement = contentRef.current
    if (contentElement) {
      contentElement.addEventListener("click", handleCommentIconClick as any)

      return () => {
        contentElement.removeEventListener(
          "click",
          handleCommentIconClick as any
        )
      }
    }
  }, [viewComments])

  return (
    <>
      <div
        ref={contentRef}
        className={className}
        dangerouslySetInnerHTML={{ __html: renderedContent }}
      />

      {contextMenuPosition && (
        <CommentContextMenu
          x={contextMenuPosition.x}
          y={contextMenuPosition.y}
          onAddComment={() => openCommentModal("add")}
          onRequestComment={() => openCommentModal("request")}
          onClose={closeContextMenu}
        />
      )}

      {isCommentModalOpen && (
        <SimpleCommentModal
          isOpen={isCommentModalOpen}
          onClose={closeCommentModal}
          onSave={addComment}
          selectedText={selectedText}
          mode={commentModalMode}
          teamMembers={teamMembers}
        />
      )}

      {activeCommentId && (
        <SimpleCommentViewer
          comments={getActiveComments()}
          onClose={closeCommentViewer}
          onAddResponse={addResponseToComment}
        />
      )}
    </>
  )
}

export default CommentableContent
