import React, { useEffect, useState } from "react"
import {
  <PERSON><PERSON>hart as AreaChartIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  ChevronDown,
  <PERSON><PERSON>hart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>atter<PERSON>hartIcon,
} from "lucide-react"
import {
  Area,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import chartTypesConfig from "./chartTypes.json"
import Ebitda<PERSON><PERSON> from "./EbitdaChart"
import MarginProgression<PERSON><PERSON> from "./MarginProgressionChart"
import ProductPerformanceChart from "./ProductPerformanceChart"

interface ChartContainerProps {
  title: string
  onDrillDown?: () => void
  showDrillDown?: boolean
  data?: any[]
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  onDrillDown = () => {},
  showDrillDown = true,
  data,
}) => {
  // State to track if the chart is being hovered over
  const [isHovered, setIsHovered] = useState(false)
  // State to control the drilldown modal visibility
  const [showDrilldownModal, setShowDrilldownModal] = useState(false)
  // State for chart type selection
  const [selectedChartType, setSelectedChartType] = useState("")
  // State to control chart type dropdown visibility
  const [showChartTypeDropdown, setShowChartTypeDropdown] = useState(false)

  // Determine the initial chart type based on the title
  useEffect(() => {
    if (title.includes("Margin")) {
      setSelectedChartType("area")
    } else if (title.includes("EBITDA")) {
      setSelectedChartType("line")
    } else {
      setSelectedChartType("bar")
    }
  }, [title])

  // Get chart type details from config
  const getChartTypeDetails = (typeId: string) => {
    return (
      chartTypesConfig.chartTypes.find((chart) => chart.id === typeId) ||
      chartTypesConfig.chartTypes[0]
    )
  }

  // Get chart type icon component
  const getChartIcon = (typeId: string) => {
    switch (typeId) {
      case "area":
        return <AreaChartIcon size={16} />
      case "bar":
        return <BarChartIcon size={16} />
      case "line":
        return <LineChartIcon size={16} />
      case "pie":
        return <PieChartIcon size={16} />
      case "scatter":
        return <ScatterChartIcon size={16} />
      default:
        return <BarChartIcon size={16} />
    }
  }
  // Render the appropriate chart based on the title
  const renderChart = () => {
    if (title === "Quarterly Gross Margin Progression") {
      return <MarginProgressionChart data={data || []} />
    } else if (title === "Product Line Contribution Margin") {
      return <ProductPerformanceChart />
    } else if (title === "EBITDA Trend") {
      return <EbitdaChart />
    } else {
      // Fallback for any other chart types
      return (
        <div className="flex aspect-video items-center justify-center border border-gray-100 bg-gray-50">
          <p className="text-gray-500">Chart visualization for {title}</p>
        </div>
      )
    }
  }

  return (
    <div
      className="leviathan-document-chart relative mb-6 rounded bg-white p-4"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <h3 className="text-primary mb-3 text-sm font-medium">{title}</h3>

      {/* Double chevron drilldown icon that appears on hover */}
      {showDrillDown && isHovered && (
        <div className="absolute top-3 right-3 z-10">
          <button
            className="text-primary hover:text-primary rounded-full bg-white p-1 shadow-md transition-colors duration-150 ease-in-out hover:bg-gray-50"
            onClick={() => setShowDrilldownModal(true)}
            title="Drill Down"
          >
            <div className="flex h-6 w-6 flex-col items-center justify-center">
              <ChevronDown size={14} strokeWidth={2.5} className="-mb-2" />
              <ChevronDown size={14} strokeWidth={2.5} className="-mt-2" />
            </div>
          </button>
        </div>
      )}

      {renderChart()}

      {/* Keep the existing drill down button at the bottom if needed */}
      {showDrillDown && (
        <div className="mt-2 flex justify-end">
          <button
            className="text-primary flex items-center text-xs font-medium"
            onClick={() => setShowDrilldownModal(true)}
          >
            <span>Drill Down</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="ml-1 h-3 w-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Drilldown Modal */}
      {showDrilldownModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ backgroundColor: "rgba(255, 255, 255, 0.5)" }}
        >
          <div className="flex max-h-[90vh] w-full max-w-7xl flex-col overflow-hidden rounded-lg bg-white shadow-xl">
            <div className="flex items-center justify-between border-b p-4">
              <h2 className="text-primary text-xl font-semibold">
                Detailed Analysis: {title}
              </h2>
              <button
                onClick={() => setShowDrilldownModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="flex-grow overflow-auto p-4">
              <div className="grid h-full grid-cols-12 gap-4">
                {/* Left sidebar with accordions for dimensions, measures, and filters */}
                <div className="col-span-3 flex h-full flex-col overflow-auto">
                  {/* Accordion container */}
                  <div className="sticky top-0 space-y-2">
                    {/* Filters accordion */}
                    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
                      <div
                        className="flex cursor-pointer items-center justify-between bg-gray-50 p-3 hover:bg-gray-100"
                        onClick={() => {
                          const filterSection =
                            document.getElementById("filterSection")
                          if (filterSection) {
                            filterSection.classList.toggle("hidden")
                          }
                        }}
                      >
                        <h3 className="text-primary font-medium">Filters</h3>
                        <div className="flex items-center">
                          <button className="text-primary mr-2 text-xs font-medium hover:underline">
                            + Add
                          </button>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                      <div
                        id="filterSection"
                        className="border-t border-gray-200 p-3"
                      >
                        <p className="mb-2 text-xs text-gray-600">
                          Limit or refine data
                        </p>
                        <div className="space-y-1.5">
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Time: Q1-Q4 2024
                            </span>
                            <span className="rounded bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800">
                              Date
                            </span>
                          </div>
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Region: North America
                            </span>
                            <span className="rounded bg-green-100 px-1.5 py-0.5 text-xs text-green-800">
                              Geography
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Dimensions accordion */}
                    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
                      <div
                        className="flex cursor-pointer items-center justify-between bg-gray-50 p-3 hover:bg-gray-100"
                        onClick={() => {
                          const dimensionSection =
                            document.getElementById("dimensionSection")
                          if (dimensionSection) {
                            dimensionSection.classList.toggle("hidden")
                          }
                        }}
                      >
                        <h3 className="text-primary font-medium">Dimensions</h3>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      <div
                        id="dimensionSection"
                        className="border-t border-gray-200 p-3"
                      >
                        <p className="mb-2 text-xs text-gray-600">
                          Qualitative attributes
                        </p>
                        <div className="space-y-1.5">
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Time Period
                            </span>
                            <span className="rounded bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800">
                              Date
                            </span>
                          </div>
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Region
                            </span>
                            <span className="rounded bg-green-100 px-1.5 py-0.5 text-xs text-green-800">
                              Geography
                            </span>
                          </div>
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Product Category
                            </span>
                            <span className="rounded bg-purple-100 px-1.5 py-0.5 text-xs text-purple-800">
                              Category
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Measures accordion */}
                    <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
                      <div
                        className="flex cursor-pointer items-center justify-between bg-gray-50 p-3 hover:bg-gray-100"
                        onClick={() => {
                          const measureSection =
                            document.getElementById("measureSection")
                          if (measureSection) {
                            measureSection.classList.toggle("hidden")
                          }
                        }}
                      >
                        <h3 className="text-primary font-medium">Measures</h3>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                      <div
                        id="measureSection"
                        className="border-t border-gray-200 p-3"
                      >
                        <p className="mb-2 text-xs text-gray-600">
                          Quantitative values
                        </p>
                        <div className="space-y-1.5">
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Revenue
                            </span>
                            <span className="rounded bg-yellow-100 px-1.5 py-0.5 text-xs text-yellow-800">
                              Currency
                            </span>
                          </div>
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Gross Margin
                            </span>
                            <span className="rounded bg-yellow-100 px-1.5 py-0.5 text-xs text-yellow-800">
                              Percentage
                            </span>
                          </div>
                          <div className="flex items-center justify-between rounded border border-gray-200 bg-white p-1.5">
                            <span className="text-sm font-medium text-gray-700">
                              Units Sold
                            </span>
                            <span className="rounded bg-red-100 px-1.5 py-0.5 text-xs text-red-800">
                              Count
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Main visualization area - now larger */}
                <div className="col-span-9 flex h-full flex-col">
                  {/* Visualization area */}
                  <div className="flex flex-grow flex-col rounded-lg border border-gray-200 bg-white p-4">
                    <div className="mb-4 flex items-center justify-between border-b pb-3">
                      <h3 className="text-lg font-medium text-gray-800">
                        Visualization
                      </h3>
                      <div className="relative flex items-center">
                        <span className="mr-2 text-sm text-gray-500">
                          Chart Type:
                        </span>
                        <div className="relative">
                          <button
                            className="flex items-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50"
                            onClick={() =>
                              setShowChartTypeDropdown(!showChartTypeDropdown)
                            }
                          >
                            <span className="flex items-center">
                              <span className="text-primary mr-1.5">
                                {getChartIcon(selectedChartType)}
                              </span>
                              <span>
                                {getChartTypeDetails(selectedChartType).name}
                              </span>
                            </span>
                            <ChevronDown
                              size={14}
                              className="ml-1.5 text-gray-400"
                            />
                          </button>

                          {/* Chart type dropdown */}
                          {showChartTypeDropdown && (
                            <div className="absolute top-full right-0 z-10 mt-1 w-56 rounded-md border border-gray-200 bg-white py-1 shadow-lg">
                              <div className="border-b border-gray-100 px-2 py-1 text-xs text-gray-500">
                                Select chart type
                              </div>
                              {chartTypesConfig.chartTypes.map((chartType) => (
                                <button
                                  key={chartType.id}
                                  className={`flex w-full items-center px-3 py-2 text-left text-sm hover:bg-gray-50 ${selectedChartType === chartType.id ? "text-primary bg-gray-50" : "text-gray-700"}`}
                                  onClick={() => {
                                    setSelectedChartType(chartType.id)
                                    setShowChartTypeDropdown(false)
                                  }}
                                >
                                  <span className="mr-2">
                                    {getChartIcon(chartType.id)}
                                  </span>
                                  <div>
                                    <div className="font-medium">
                                      {chartType.name}
                                    </div>
                                    <div className="mt-0.5 text-xs text-gray-500">
                                      {chartType.description}
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mb-4 rounded-md border border-gray-200 bg-gray-50 p-3">
                      <div className="mb-2 flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-700">
                          Current Data Selected
                        </h4>
                        <div className="text-xs text-gray-500">
                          {getChartTypeDetails(selectedChartType)
                            .rules.map((rule, index) => (
                              <div
                                key={index}
                                className={`flex items-center ${rule.severity === "error" ? "text-red-600" : rule.severity === "warning" ? "text-amber-600" : "text-blue-600"}`}
                              >
                                {rule.severity === "error" && (
                                  <span className="mr-1">⚠️</span>
                                )}
                                {rule.severity === "warning" && (
                                  <span className="mr-1">ℹ️</span>
                                )}
                                <span className="truncate">
                                  {rule.description}
                                </span>
                              </div>
                            ))
                            .slice(0, 1)}
                        </div>
                      </div>

                      {/* DrillDownChip Component */}
                      <div className="mb-3">
                        <h5 className="mb-1 text-xs font-medium text-gray-700">
                          Drill-Down Options
                        </h5>
                        <div className="flex flex-wrap gap-2">
                          <div className="group flex cursor-pointer items-center rounded-md border border-gray-200 bg-white px-2 py-1 hover:bg-gray-50">
                            <span className="mr-1 text-sm text-gray-700">
                              Q1-Q4 2024
                            </span>
                            <div className="flex items-center rounded bg-blue-100 px-1.5 py-0.5 text-blue-800">
                              <span className="mr-1 text-xs">Date</span>
                              <div className="flex h-3 w-3 flex-col items-center justify-center">
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mb-1.5"
                                />
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mt-1.5"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="group flex cursor-pointer items-center rounded-md border border-gray-200 bg-white px-2 py-1 hover:bg-gray-50">
                            <span className="mr-1 text-sm text-gray-700">
                              North America
                            </span>
                            <div className="flex items-center rounded bg-green-100 px-1.5 py-0.5 text-green-800">
                              <span className="mr-1 text-xs">Geography</span>
                              <div className="flex h-3 w-3 flex-col items-center justify-center">
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mb-1.5"
                                />
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mt-1.5"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="group flex cursor-pointer items-center rounded-md border border-gray-200 bg-white px-2 py-1 hover:bg-gray-50">
                            <span className="mr-1 text-sm text-gray-700">
                              Product Category
                            </span>
                            <div className="flex items-center rounded bg-purple-100 px-1.5 py-0.5 text-purple-800">
                              <span className="mr-1 text-xs">Category</span>
                              <div className="flex h-3 w-3 flex-col items-center justify-center">
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mb-1.5"
                                />
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mt-1.5"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <p className="mb-1 text-xs text-gray-500">
                            X-Axis (Dimension)
                          </p>
                          <div className="group flex cursor-pointer items-center justify-between rounded border border-gray-200 bg-white p-2 hover:bg-gray-50">
                            <span className="text-sm font-medium text-gray-700">
                              Time Period
                            </span>
                            <div className="flex items-center rounded bg-blue-100 px-1.5 py-0.5 text-blue-800">
                              <span className="mr-1 text-xs">Date</span>
                              <div className="flex h-3 w-3 flex-col items-center justify-center">
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mb-1.5"
                                />
                                <ChevronDown
                                  size={8}
                                  strokeWidth={2.5}
                                  className="-mt-1.5"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="mt-1 text-xs text-gray-500">
                            <span className="text-primary">
                              Compatible dimensions:{" "}
                            </span>
                            {getChartTypeDetails(selectedChartType)
                              .allowedDimensions.map((dim) => {
                                const dimType =
                                  chartTypesConfig.dimensionTypes.find(
                                    (d) => d.id === dim
                                  )
                                return dimType ? dimType.name : dim
                              })
                              .join(", ")}
                          </div>
                        </div>
                        <div>
                          <p className="mb-1 text-xs text-gray-500">Measures</p>

                          {/* Primary Measure */}
                          <div className="mb-2">
                            <div className="group flex cursor-pointer items-center justify-between rounded border border-gray-200 bg-white p-2 hover:bg-gray-50">
                              <div className="flex items-center">
                                <div className="mr-2 h-3 w-3 rounded-full bg-blue-500"></div>
                                <span className="text-sm font-medium text-gray-700">
                                  {title.includes("Margin")
                                    ? "Gross Margin %"
                                    : "Revenue"}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center rounded bg-yellow-100 px-1.5 py-0.5 text-yellow-800">
                                  <span className="mr-1 text-xs">
                                    {title.includes("Margin")
                                      ? "Percentage"
                                      : "Currency"}
                                  </span>
                                  <div className="flex h-3 w-3 flex-col items-center justify-center">
                                    <ChevronDown
                                      size={8}
                                      strokeWidth={2.5}
                                      className="-mb-1.5"
                                    />
                                    <ChevronDown
                                      size={8}
                                      strokeWidth={2.5}
                                      className="-mt-1.5"
                                    />
                                  </div>
                                </div>
                                <div className="relative">
                                  <button
                                    className="flex h-6 w-6 items-center justify-center rounded hover:bg-gray-100"
                                    onClick={() => {
                                      /* Toggle dropdown */
                                    }}
                                  >
                                    {getChartIcon("area")}
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Secondary Measure (example of additional measure) */}
                          <div className="mb-2">
                            <div className="group flex cursor-pointer items-center justify-between rounded border border-gray-200 bg-white p-2 hover:bg-gray-50">
                              <div className="flex items-center">
                                <div className="mr-2 h-3 w-3 rounded-full bg-green-500"></div>
                                <span className="text-sm font-medium text-gray-700">
                                  Profit Margin
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center rounded bg-yellow-100 px-1.5 py-0.5 text-yellow-800">
                                  <span className="mr-1 text-xs">
                                    Percentage
                                  </span>
                                  <div className="flex h-3 w-3 flex-col items-center justify-center">
                                    <ChevronDown
                                      size={8}
                                      strokeWidth={2.5}
                                      className="-mb-1.5"
                                    />
                                    <ChevronDown
                                      size={8}
                                      strokeWidth={2.5}
                                      className="-mt-1.5"
                                    />
                                  </div>
                                </div>
                                <div className="relative">
                                  <button
                                    className="flex h-6 w-6 items-center justify-center rounded hover:bg-gray-100"
                                    onClick={() => {
                                      /* Toggle dropdown */
                                    }}
                                  >
                                    {getChartIcon("line")}
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Add Measure Button */}
                          <button className="flex w-full items-center justify-center rounded border border-dashed border-gray-300 p-1.5 text-xs text-gray-500 hover:bg-gray-50">
                            <span className="mr-1">+</span> Add another measure
                          </button>

                          <div className="mt-3 text-xs text-gray-500">
                            <span className="text-primary">
                              Compatible measures:{" "}
                            </span>
                            {getChartTypeDetails(selectedChartType)
                              .allowedMeasures.map((meas) => {
                                const measType =
                                  chartTypesConfig.measureTypes.find(
                                    (m) => m.id === meas
                                  )
                                return measType ? measType.name : meas
                              })
                              .join(", ")}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-grow flex-col overflow-hidden rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between border-b border-gray-200 bg-gray-50 p-3">
                        <h4 className="text-sm font-medium text-gray-700">
                          Chart Preview
                        </h4>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center">
                            <div className="mr-1 h-3 w-3 rounded-full bg-blue-500"></div>
                            <span className="text-xs text-gray-600">
                              {title.includes("Margin")
                                ? "Gross Margin %"
                                : "Revenue"}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <div className="mr-1 h-3 w-3 rounded-full bg-green-500"></div>
                            <span className="text-xs text-gray-600">
                              Profit Margin
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-grow items-center justify-center p-4">
                        {/* Combined Chart Visualization */}
                        <div className="h-64 w-full">
                          <ResponsiveContainer width="100%" height="100%">
                            <ComposedChart
                              data={[
                                { month: "Jan", revenue: 4000, profit: 24 },
                                { month: "Feb", revenue: 3000, profit: 18 },
                                { month: "Mar", revenue: 5000, profit: 29 },
                                { month: "Apr", revenue: 7000, profit: 34 },
                                { month: "May", revenue: 6000, profit: 32 },
                                { month: "Jun", revenue: 8000, profit: 38 },
                              ]}
                              margin={{
                                top: 10,
                                right: 30,
                                left: 0,
                                bottom: 0,
                              }}
                            >
                              <CartesianGrid
                                strokeDasharray="3 3"
                                vertical={false}
                                opacity={0.3}
                              />
                              <XAxis dataKey="month" />
                              <YAxis yAxisId="left" />
                              <YAxis yAxisId="right" orientation="right" />
                              <Tooltip
                                formatter={(value, name) => {
                                  if (name === "profit")
                                    return [`${value}%`, "Profit Margin"]
                                  return [`$${value}`, "Revenue"]
                                }}
                              />
                              <Legend />
                              <Area
                                yAxisId="left"
                                type="monotone"
                                dataKey="revenue"
                                fill="rgba(59, 130, 246, 0.2)"
                                stroke="#3b82f6"
                                name="Revenue"
                              />
                              <Line
                                yAxisId="right"
                                type="monotone"
                                dataKey="profit"
                                stroke="#22c55e"
                                strokeWidth={2}
                                dot={{ r: 4, fill: "#22c55e" }}
                                name="Profit Margin"
                              />
                            </ComposedChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-grow items-center justify-center rounded-lg border border-dashed border-gray-200 p-4">
                      <div className="max-w-2xl p-6 text-center">
                        <svg
                          className="mx-auto mb-4 h-24 w-24 text-gray-300"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                          ></path>
                        </svg>
                        <p className="mb-2 text-gray-500">
                          Modify dimensions and measures to update visualization
                        </p>
                        <p className="text-sm text-gray-400">
                          Drag additional items to enhance your analysis
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between border-t bg-gray-50 p-4">
              <div>
                <button
                  className="mr-2 rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100"
                  onClick={() => setShowDrilldownModal(false)}
                >
                  Cancel
                </button>
              </div>
              <div>
                <button className="mr-2 rounded-md bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300">
                  Save View
                </button>
                <button
                  className="bg-primary hover:bg-primary/90 rounded-md px-4 py-2 text-white"
                  onClick={onDrillDown}
                >
                  Apply & Export
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ChartContainer
