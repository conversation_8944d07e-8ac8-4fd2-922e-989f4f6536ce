import React from 'react';
import { 
  ComposedChart, 
  Line, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend,
  ReferenceLine
} from 'recharts';

// Sample data for EBITDA trend
const ebitdaData = [
  { 
    quarter: 'Q1 2023', 
    ebitda: 4.8, 
    ebitdaMargin: 14.5,
    industryAvg: 13.8
  },
  { 
    quarter: 'Q2 2023', 
    ebitda: 5.2, 
    ebitdaMargin: 15.1,
    industryAvg: 13.9
  },
  { 
    quarter: 'Q3 2023', 
    ebitda: 5.6, 
    ebitdaMargin: 15.8,
    industryAvg: 14.0
  },
  { 
    quarter: 'Q4 2023', 
    ebitda: 5.9, 
    ebitdaMargin: 16.2,
    industryAvg: 14.1
  },
  { 
    quarter: 'Q1 2024', 
    ebitda: 5.5, 
    ebitdaMargin: 17.2,
    industryAvg: 14.2
  },
  { 
    quarter: 'Q2 2024', 
    ebitda: 5.9, 
    ebitdaMargin: 17.8,
    industryAvg: 14.3
  },
  { 
    quarter: 'Q3 2024', 
    ebitda: 6.3, 
    ebitdaMargin: 18.2,
    industryAvg: 14.4
  },
  { 
    quarter: 'Q4 2024', 
    ebitda: 6.6, 
    ebitdaMargin: 18.7,
    industryAvg: 14.5
  }
];

const EbitdaChart: React.FC = () => {
  // Format numbers for tooltip
  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };
  
  const formatCurrency = (value: number) => {
    return `$${value.toFixed(1)}M`;
  };
  
  // Custom tooltip to show all relevant metrics
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-sm rounded-md">
          <p className="font-medium text-sm text-gray-800">{label}</p>
          <div className="mt-2 space-y-1">
            <p className="text-xs">
              <span className="inline-block w-28 text-gray-800">EBITDA:</span> 
              <span className="font-medium text-black">{formatCurrency(payload[0].value)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-28 text-gray-800">EBITDA Margin:</span> 
              <span className="font-medium text-black">{formatPercent(payload[1].value)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-28 text-gray-800">Industry Avg:</span> 
              <span className="font-medium text-black">{formatPercent(payload[2].value)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-28 text-gray-800">vs Industry:</span> 
              <span className="font-medium text-[#2EC4B6]">
                {(payload[1].value - payload[2].value) > 0 ? '+' : ''}{formatPercent(payload[1].value - payload[2].value)}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <ComposedChart
        data={ebitdaData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="quarter" 
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
        />
        <YAxis 
          yAxisId="left"
          tickFormatter={formatCurrency}
          domain={[0, 8]}
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
          width={50}
          label={{ 
            value: 'EBITDA (in millions)', 
            angle: -90, 
            position: 'insideLeft',
            style: { textAnchor: 'middle', fontSize: 12, fill: '#6B7280' }
          }}
        />
        <YAxis 
          yAxisId="right"
          orientation="right"
          tickFormatter={formatPercent}
          domain={[10, 20]}
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
          width={50}
          label={{ 
            value: 'Margin %', 
            angle: 90, 
            position: 'insideRight',
            style: { textAnchor: 'middle', fontSize: 12, fill: '#6B7280' }
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          formatter={(value) => <span className="text-xs font-medium">{value}</span>}
          wrapperStyle={{ paddingTop: 10 }}
        />
        <Bar 
          yAxisId="left"
          dataKey="ebitda" 
          fill="#0A2342" 
          name="EBITDA ($M)"
          barSize={20}
          radius={[2, 2, 0, 0]}
        />
        <Line 
          yAxisId="right"
          type="monotone" 
          dataKey="ebitdaMargin" 
          stroke="#2EC4B6" 
          strokeWidth={2}
          name="EBITDA Margin (%)"
          dot={{ r: 4, fill: '#2EC4B6', stroke: '#0A2342', strokeWidth: 1 }}
          activeDot={{ r: 6, fill: '#2EC4B6', stroke: '#0A2342', strokeWidth: 1 }}
        />
        <Line 
          yAxisId="right"
          type="monotone" 
          dataKey="industryAvg" 
          stroke="#6B7280" 
          strokeDasharray="4 4"
          strokeWidth={2}
          name="Industry Average (%)"
          dot={{ r: 3, fill: '#6B7280', stroke: '#4B5563', strokeWidth: 1 }}
        />
        <ReferenceLine 
          yAxisId="right"
          y={16.2} 
          stroke="#0A2342" 
          strokeDasharray="3 3"
          label={{ 
            value: 'Target', 
            position: 'right',
            fill: '#0A2342',
            fontSize: 11
          }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default EbitdaChart;
