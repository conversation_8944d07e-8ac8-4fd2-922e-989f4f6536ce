import { useCallback, useEffect, useState } from "react"

import { Comment } from "./CommentViewer"

interface CommentData {
  id: string
  textContent: string
  selectionStart: number
  selectionEnd: number
  comments: Comment[]
}

// Mock team members for demonstration
const TEAM_MEMBERS = [
  { id: "1", name: "<PERSON>" },
  { id: "2", name: "<PERSON>" },
  { id: "3", name: "<PERSON>" },
  { id: "4", name: "<PERSON>" },
  { id: "5", name: "<PERSON>" },
]

const useComments = (sectionId: string) => {
  const [comments, setComments] = useState<CommentData[]>([])
  const [selectedText, setSelectedText] = useState("")
  const [selectionRange, setSelectionRange] = useState<{
    start: number
    end: number
  } | null>(null)
  const [contextMenuPosition, setContextMenuPosition] = useState<{
    x: number
    y: number
  } | null>(null)
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false)
  const [commentModalMode, setCommentModalMode] = useState<"add" | "request">(
    "add"
  )
  const [activeCommentId, setActiveCommentId] = useState<string | null>(null)

  // Load comments from localStorage on mount
  useEffect(() => {
    const savedComments = localStorage.getItem(`comments-${sectionId}`)
    if (savedComments) {
      try {
        setComments(JSON.parse(savedComments))
      } catch (error) {
        console.error("Failed to parse saved comments:", error)
      }
    }
  }, [sectionId])

  // Save comments to localStorage when they change
  useEffect(() => {
    if (comments.length > 0) {
      localStorage.setItem(`comments-${sectionId}`, JSON.stringify(comments))
    }
  }, [comments, sectionId])

  // We're not going to automatically track text selection anymore
  // Instead, we'll only capture it when the context menu is opened

  const handleContextMenu = useCallback((e: MouseEvent) => {
    // Get the selection at the time of right-click
    const selection = window.getSelection()
    const text = selection?.toString().trim() || ""

    if (text.length > 0) {
      // We have text selected, show the context menu
      e.preventDefault()
      setSelectedText(text)
      setSelectionRange({
        start: 0,
        end: text.length,
      })
      setContextMenuPosition({ x: e.clientX, y: e.clientY })
    }
  }, [])

  const closeContextMenu = useCallback(() => {
    setContextMenuPosition(null)
  }, [])

  const openCommentModal = useCallback(
    (mode: "add" | "request") => {
      setCommentModalMode(mode)
      setIsCommentModalOpen(true)
      closeContextMenu()
    },
    [closeContextMenu]
  )

  const closeCommentModal = useCallback(() => {
    setIsCommentModalOpen(false)
  }, [])

  const addComment = useCallback(
    (commentText: string) => {
      if (selectedText) {
        const newComment: Comment = {
          id: Date.now().toString(),
          text: commentText,
          author: "You", // In a real app, this would come from the user's profile
          timestamp: new Date().toISOString(),
          isRequest: commentModalMode === "request",
        }

        if (commentModalMode === "request") {
          try {
            const requestData = JSON.parse(commentText)
            newComment.text = `Comment requested from team members`
            newComment.requestedFrom = requestData.teamMembers.map(
              (id: string) => {
                const member = TEAM_MEMBERS.find((m) => m.id === id)
                return member ? member.name : id
              }
            )
          } catch (e) {
            console.error("Failed to parse request data:", e)
          }
        }

        // Check if we already have a comment for this exact text selection
        const existingCommentIndex = comments.findIndex(
          (c) => c.textContent === selectedText
        )

        if (existingCommentIndex !== -1) {
          // Add to existing comment
          const updatedComments = [...comments]
          updatedComments[existingCommentIndex] = {
            ...updatedComments[existingCommentIndex],
            comments: [
              ...updatedComments[existingCommentIndex].comments,
              newComment,
            ],
          }
          setComments(updatedComments)
        } else {
          // Create new comment data
          const newCommentData: CommentData = {
            id: Date.now().toString(),
            textContent: selectedText,
            selectionStart: 0, // We're simplifying this approach
            selectionEnd: selectedText.length,
            comments: [newComment],
          }
          setComments((prev) => [...prev, newCommentData])
        }

        closeCommentModal()
        setSelectedText("")
        setSelectionRange(null)
        setContextMenuPosition(null)
      }
    },
    [selectedText, commentModalMode, comments, closeCommentModal]
  )

  const addResponseToComment = useCallback(
    (commentId: string, responseText: string) => {
      setComments((prev) => {
        return prev.map((commentData) => {
          const updatedComments = commentData.comments.map((comment) => {
            if (comment.id === commentId) {
              // Add a new comment as a response
              const responseComment: Comment = {
                id: Date.now().toString(),
                text: responseText,
                author: "You", // In a real app, this would come from the user's profile
                timestamp: new Date().toISOString(),
              }

              // In a real app, you might want to create a proper thread structure
              // For now, we're just adding a new comment to the same thread
              return {
                ...comment,
                isRequest: false, // Mark the request as fulfilled
              }
            }
            return comment
          })

          // Add the response as a new comment in the same thread
          if (commentData.comments.some((c) => c.id === commentId)) {
            const responseComment: Comment = {
              id: Date.now().toString(),
              text: responseText,
              author: "You", // In a real app, this would come from the user's profile
              timestamp: new Date().toISOString(),
            }
            return {
              ...commentData,
              comments: [...updatedComments, responseComment],
            }
          }

          return {
            ...commentData,
            comments: updatedComments,
          }
        })
      })
    },
    []
  )

  const viewComments = useCallback((commentId: string) => {
    setActiveCommentId(commentId)
  }, [])

  const closeCommentViewer = useCallback(() => {
    setActiveCommentId(null)
  }, [])

  const getActiveComments = useCallback(() => {
    if (!activeCommentId) return []
    const commentData = comments.find((c) => c.id === activeCommentId)
    return commentData ? commentData.comments : []
  }, [activeCommentId, comments])

  // Only set up the context menu event listener
  useEffect(() => {
    document.addEventListener("contextmenu", handleContextMenu as any)

    return () => {
      document.removeEventListener("contextmenu", handleContextMenu as any)
    }
  }, [handleContextMenu])

  return {
    comments,
    selectedText,
    contextMenuPosition,
    isCommentModalOpen,
    commentModalMode,
    activeCommentId,
    teamMembers: TEAM_MEMBERS,
    closeContextMenu,
    openCommentModal,
    closeCommentModal,
    addComment,
    viewComments,
    closeCommentViewer,
    getActiveComments,
    addResponseToComment,
  }
}

export default useComments
