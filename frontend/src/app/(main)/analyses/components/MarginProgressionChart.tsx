import React from 'react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  ReferenceLine,
  Legend
} from 'recharts';

interface MarginProgressionChartProps {
  data: Array<{
    period: string;
    revenue: number;
    cogs: number;
    grossProfit: number;
    margin: number;
  }>;
}

const MarginProgressionChart: React.FC<MarginProgressionChartProps> = ({ data }) => {
  // Calculate industry average for comparison
  const industryAverage = 43.5; // Example industry average
  
  // Format numbers for tooltip
  const formatCurrency = (value: number) => {
    return `$${value.toLocaleString()}`;
  };
  
  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };
  
  // Custom tooltip to show all relevant metrics
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-sm rounded-md">
          <p className="font-medium text-sm text-gray-800">{label}</p>
          <div className="mt-2 space-y-1">
            <p className="text-xs">
              <span className="inline-block w-24 text-gray-800">Revenue:</span> 
              <span className="font-medium text-black">{formatCurrency(payload[0].payload.revenue)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-24 text-gray-800">COGS:</span> 
              <span className="font-medium text-black">{formatCurrency(payload[0].payload.cogs)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-24 text-gray-800">Gross Profit:</span> 
              <span className="font-medium text-black">{formatCurrency(payload[0].payload.grossProfit)}</span>
            </p>
            <p className="text-xs">
              <span className="inline-block w-24 text-gray-800">Margin:</span> 
              <span className="font-medium text-[#2EC4B6]">{formatPercent(payload[0].payload.margin)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart
        data={data}
        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
      >
        <defs>
          <linearGradient id="colorMargin" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#2EC4B6" stopOpacity={0.8} />
            <stop offset="95%" stopColor="#2EC4B6" stopOpacity={0.1} />
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="period" 
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
        />
        <YAxis 
          tickFormatter={formatPercent}
          domain={[40, 50]}
          tick={{ fontSize: 12, fill: '#4B5563' }}
          axisLine={{ stroke: '#E5E7EB' }}
          tickLine={{ stroke: '#E5E7EB' }}
          width={45}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          verticalAlign="top" 
          height={36}
          formatter={(value) => <span className="text-xs font-medium">{value}</span>}
        />
        <ReferenceLine 
          y={industryAverage} 
          stroke="#0A2342" 
          strokeDasharray="3 3"
          label={{ 
            value: 'Industry Avg', 
            position: 'insideBottomRight',
            fill: '#0A2342',
            fontSize: 11
          }}
        />
        <Area 
          type="monotone" 
          dataKey="margin" 
          stroke="#2EC4B6" 
          strokeWidth={2}
          fillOpacity={1} 
          fill="url(#colorMargin)" 
          name="Gross Margin %"
          activeDot={{ r: 6, stroke: '#0A2342', strokeWidth: 1, fill: '#2EC4B6' }}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default MarginProgressionChart;
