// Common types for the Leviathan project

// Filter types for analysis
export type AnalysisFilter = {
  id: string
  field: string
  operator:
    | "equals"
    | "contains"
    | "greater_than"
    | "less_than"
    | "between"
    | "in"
  value: string | number | string[] | number[]
  label?: string
}

// Measure types for analysis
export type AnalysisMeasure = {
  id: string
  field: string
  aggregation: "sum" | "avg" | "min" | "max" | "count"
  label?: string
  format?: "currency" | "percentage" | "number"
}

export type AnalysisView = {
  id: number
  name: string
  health?: "red" | "amber" | "green"
  parentId?: number
  children?: AnalysisView[]
  filters?: AnalysisFilter[]
  measures?: AnalysisMeasure[]
  createdAt?: string // Date when the analysis was created
  favorite?: boolean // Whether the analysis is favorited
}

export type AnalysisSection = {
  id: string
  title: string
  content: string
}

export type AnalysisVersion = {
  id: string
  isBase: boolean // true if this is the BASE version
  createdAt: string // Date when the snapshot was created
}

export type AnalysisData = {
  title: string
  summary: string
  sections: AnalysisSection[]
  data: {
    quarterlyResults: QuarterlyResult[]
  }
  currentVersion: AnalysisVersion
  versions?: AnalysisVersion[] // History of previous versions/snapshots
}

export type QuarterlyResult = {
  period: string
  revenue: number
  cogs: number
  grossProfit: number
  margin: number
}

export type ExtendedRowData = QuarterlyResult & {
  source?: string
  unit?: string
}

export type Filter = {
  id: number
  value: string
  color: string
}

export type DataSource = {
  id: number
  name: string
  color: string
}

export type AnalysisType =
  | "profitability"
  | "cashflow"
  | "revenue"
  | "expense"
  | "balance"
  | "working-capital"
  | "investment"
  | "valuation"
  | "operational"
  | "risk"
export type Currency = "usd" | "sgd" | "krw" | "aud"
export type TimePeriod = "quarterly" | "monthly" | "yearly"

export type ChartType =
  | "bar"
  | "line"
  | "area"
  | "pie"
  | "donut"
  | "scatter"
  | "bubble"
  | "radar"
  | "heatmap"
  | "table"
  | "waterfall"
  | "boxplot"
  | "violin"
  | "treemap"
  | "sankey"

export type ChartDimension = {
  id: string
  name: string
  field: string
  type: "category" | "measure" | "time"
  aggregation?: "sum" | "avg" | "min" | "max" | "count"
  format?: string
}

export type Chart = {
  id: string
  name: string
  type: ChartType
  secondaryChartType?: ChartType // For combined charts with multiple dimensions
  order: number // Ordinal position for narrative flow
  dataTable?: string // Reference to a single data source/table (Django ORM) - deprecated
  dataTables?: string[] // References to multiple data sources/tables (Django ORM)
  dimensions: {
    x?: ChartDimension
    y?: ChartDimension[]
    color?: ChartDimension
    size?: ChartDimension
    tooltip?: ChartDimension[]
  }
  filters?: AnalysisFilter[]
  settings?: {
    stacked?: boolean
    showLegend?: boolean
    showValues?: boolean
    showTrendline?: boolean
    showGrid?: boolean
    showAxis?: boolean
    title?: string
    subtitle?: string
  }
  description?: string // For AI-generated insights or manual annotations
}
