import React, { useState } from "react"

import CashConversion<PERSON>hart from "./CashConversionChart"
import CashFlowChart from "./CashFlowChart"
import { cashFlowAnalysisData } from "./cashFlowData"
import CommentableContent from "./CommentableContent"

// Define the cash flow data types
interface CashFlowQuarterlyResult {
  period: string
  operatingCashFlow: number
  investingCashFlow: number
  financingCashFlow: number
  netCashFlow: number
  dso: number
  dio: number
  dpo: number
  ccc: number
}

interface CashFlowAnalysisData {
  title: string
  summary: string
  sections: Array<{
    id: string
    title: string
    content: string
  }>
  data: {
    quarterlyResults: CashFlowQuarterlyResult[]
  }
  currentVersion: {
    id: string
    isBase: boolean
    createdAt: string
  }
  versions: Array<{
    id: string
    isBase: boolean
    createdAt: string
  }>
}

const CashFlowAnalysis: React.FC = () => {
  const [showDataTable, setShowDataTable] = useState(true)
  // Use explicit casting to handle the type conversion safely
  const cashFlowData = cashFlowAnalysisData as unknown as CashFlowAnalysisData
  const { sections, data } = cashFlowData

  const onRawDataView = () => {
    // Toggle between table and raw data view
    setShowDataTable(!showDataTable)
  }

  // Find specific sections
  const overview = sections.find((section) => section.id === "overview")
  const operatingCashFlow = sections.find(
    (section) => section.id === "operating-cash-flow"
  )
  const workingCapital = sections.find(
    (section) => section.id === "working-capital"
  )
  const cashConversion = sections.find(
    (section) => section.id === "cash-conversion"
  )
  const freeCashFlow = sections.find(
    (section) => section.id === "free-cash-flow"
  )
  const outlook = sections.find((section) => section.id === "outlook")

  return (
    <div className="space-y-8">
      {/* Executive Summary */}
      {overview && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {overview.title}
          </h2>
          <CommentableContent content={overview.content} sectionId="overview" />
        </div>
      )}

      {/* Operating Cash Flow Analysis */}
      {operatingCashFlow && data?.quarterlyResults && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {operatingCashFlow.title}
          </h2>
          <CommentableContent
            content={operatingCashFlow.content}
            sectionId="operating-cash-flow"
          />
          <div className="mt-6">
            <CashFlowChart data={data.quarterlyResults as any} />
          </div>

          {/* Quarterly Data Table */}
          {showDataTable &&
            data.quarterlyResults &&
            data.quarterlyResults.length > 0 && (
              <div className="mt-6 bg-white">
                <div className="mb-6 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Quarterly Cash Flow Data
                    </h3>
                    <p className="mt-1 text-sm text-gray-600">
                      Year-over-year operating cash flow growth of{" "}
                      <span className="border-b-4 border-green-600 font-bold text-black">
                        18.4%
                      </span>{" "}
                      with consistent improvement in cash conversion metrics.
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                      Export
                    </button>
                    <button
                      className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50"
                      onClick={onRawDataView}
                    >
                      Raw Data
                    </button>
                  </div>
                </div>

                {/* High-finance style table */}
                <div className="overflow-hidden rounded border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-3 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                          Period
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                          Operating CF
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                          Investing CF
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                          Financing CF
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                          Net CF
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {data.quarterlyResults.map((row, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-3 py-2 text-sm font-medium text-gray-900">
                            {row.period}
                          </td>
                          <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            ${(row.operatingCashFlow / 1000000).toFixed(1)}M
                          </td>
                          <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            ${(row.investingCashFlow / 1000000).toFixed(1)}M
                          </td>
                          <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            ${(row.financingCashFlow / 1000000).toFixed(1)}M
                          </td>
                          <td className="px-3 py-2 text-right">
                            <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                              ${(row.netCashFlow / 1000000).toFixed(1)}M
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  <div className="border-t border-gray-200 bg-gray-50 px-3 py-2">
                    <div className="space-y-1.5">
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">Cash Flow Analysis:</span>{" "}
                        Operating cash flow shows consistent growth with{" "}
                        <span className="border-b-4 border-green-600 font-bold text-black">
                          +35.5%
                        </span>{" "}
                        increase from Q1 to Q4. Investing cash flow reflects
                        strategic capital expenditures to support growth
                        initiatives.
                      </p>
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">
                          Financing Activities:
                        </span>{" "}
                        Negative financing cash flow reflects dividend payments
                        of{" "}
                        <span className="border-b-4 border-red-600 font-bold text-black">
                          $4.2M
                        </span>{" "}
                        and debt repayments of{" "}
                        <span className="border-b-4 border-red-600 font-bold text-black">
                          $2.4M
                        </span>
                        , partially offset by new borrowing of{" "}
                        <span className="border-b-4 border-green-600 font-bold text-black">
                          $1.5M
                        </span>
                        .
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>
      )}

      {/* Working Capital Management */}
      {workingCapital && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {workingCapital.title}
          </h2>
          <CommentableContent
            content={workingCapital.content}
            sectionId="working-capital"
          />

          {/* Working Capital Metrics Table */}
          <div className="mt-6 overflow-hidden rounded border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-3 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Metric
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Q1 2024
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Q2 2024
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Q3 2024
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Q4 2024
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    YoY Change
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Days Sales Outstanding (DSO)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    44
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    42
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    40
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    38
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      -7
                    </span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Days Inventory Outstanding (DIO)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    52
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    48
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    46
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    44
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      -12
                    </span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Days Payable Outstanding (DPO)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    42
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    44
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    44
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    45
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      +5
                    </span>
                  </td>
                </tr>
                <tr className="bg-gray-50 hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-bold text-gray-900">
                    Cash Conversion Cycle
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    54
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    46
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    42
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    37
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      -19
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Cash Conversion Cycle */}
      {cashConversion && data?.quarterlyResults && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {cashConversion.title}
          </h2>
          <CommentableContent
            content={cashConversion.content}
            sectionId="cash-conversion"
          />
          <div className="mt-6">
            <CashConversionChart data={data.quarterlyResults as any} />
          </div>
        </div>
      )}

      {/* Free Cash Flow Analysis */}
      {freeCashFlow && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {freeCashFlow.title}
          </h2>
          <CommentableContent
            content={freeCashFlow.content}
            sectionId="free-cash-flow"
          />

          {/* Free Cash Flow Metrics Table */}
          <div className="mt-6 overflow-hidden rounded border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-3 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Metric
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    FY2023
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    FY2024
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    YoY Change
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                    Industry Avg
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Operating Cash Flow ($M)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    24.2
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    28.7
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      +18.4%
                    </span>
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    +12.5%
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Capital Expenditures ($M)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    10.9
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    10.3
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      -5.5%
                    </span>
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    -2.1%
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-medium text-gray-900">
                    Free Cash Flow ($M)
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    14.8
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    18.4
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      +24.3%
                    </span>
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                    +10.2%
                  </td>
                </tr>
                <tr className="bg-gray-50 hover:bg-gray-50">
                  <td className="px-3 py-2 text-sm font-bold text-gray-900">
                    FCF Margin
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    12.1%
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    14.2%
                  </td>
                  <td className="px-3 py-2 text-right">
                    <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                      +2.1pp
                    </span>
                  </td>
                  <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                    11.8%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Outlook & Recommendations */}
      {outlook && (
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            {outlook.title}
          </h2>
          <CommentableContent content={outlook.content} sectionId="outlook" />
        </div>
      )}
    </div>
  )
}

export default CashFlowAnalysis
