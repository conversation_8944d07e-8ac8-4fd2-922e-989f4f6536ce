import { apiClient } from "./apiClient"
import { API_CONFIG } from "./environment"
import { mockService } from "./mockService"
import {
  AnalysisData,
  AnalysisView,
  DataSource,
  ExtendedRowData,
} from "./types"

/**
 * API service for handling data operations
 * Uses either real API calls or mock data based on configuration
 */

/**
 * Fetch analysis views
 */
export const fetchAnalysisViews = async (): Promise<AnalysisView[]> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.getAnalysisViews()
    }

    return await apiClient.get<AnalysisView[]>("/analysis-views/")
  } catch (error) {
    console.error("Error fetching analysis views:", error)
    // Fallback to mock data in case of error
    return await mockService.getAnalysisViews()
  }
}

/**
 * Fetch analysis data for a specific view
 */
export const fetchAnalysisData = async (
  viewId: number
): Promise<AnalysisData> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.getAnalysisData(viewId)
    }

    return await apiClient.get<AnalysisData>(`/analysis-data/${viewId}/`)
  } catch (error) {
    console.error(`Error fetching analysis data for view ${viewId}:`, error)
    // Fallback to mock data in case of error
    return await mockService.getAnalysisData(viewId)
  }
}

/**
 * Fetch table data
 */
export const fetchTableData = async (): Promise<ExtendedRowData[]> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.getTableData()
    }

    return await apiClient.get<ExtendedRowData[]>("/table-data/")
  } catch (error) {
    console.error("Error fetching table data:", error)
    // Fallback to mock data in case of error
    return await mockService.getTableData()
  }
}

/**
 * Fetch data sources
 */
export const fetchDataSources = async (): Promise<DataSource[]> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.getDataSources()
    }

    return await apiClient.get<DataSource[]>("/data-sources/")
  } catch (error) {
    console.error("Error fetching data sources:", error)
    // Fallback to mock data in case of error
    return await mockService.getDataSources()
  }
}

/**
 * Save a new analysis as a sub-item of a parent analysis
 */
export const saveAnalysis = async (
  parentId: number,
  name: string,
  filters: any,
  measures: any
): Promise<AnalysisView> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.saveAnalysis(parentId, name, filters, measures)
    }

    return await apiClient.post<AnalysisView>("/analysis-views/", {
      parentId,
      name,
      filters,
      measures,
    })
  } catch (error) {
    console.error("Error saving analysis:", error)
    // Fallback to mock data in case of error
    return await mockService.saveAnalysis(parentId, name, filters, measures)
  }
}

/**
 * Update an existing analysis with new filters/measures
 */
export const updateAnalysis = async (
  viewId: number,
  filters: any,
  measures: any
): Promise<AnalysisData> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.updateAnalysis(viewId, filters, measures)
    }

    return await apiClient.put<AnalysisData>(`/analysis-data/${viewId}/`, {
      filters,
      measures,
    })
  } catch (error) {
    console.error(`Error updating analysis ${viewId}:`, error)
    // Fallback to mock data in case of error
    return await mockService.updateAnalysis(viewId, filters, measures)
  }
}

/**
 * Toggle favorite status for an analysis view
 */
export const toggleFavorite = async (
  viewId: number
): Promise<{ id: number; favorite: boolean; success: boolean }> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.toggleFavorite(viewId)
    }

    return await apiClient.post<{
      id: number
      favorite: boolean
      success: boolean
    }>(`/analysis-views/${viewId}/favorite/`)
  } catch (error) {
    console.error(`Error toggling favorite for analysis ${viewId}:`, error)
    // Return a failure response
    return { id: viewId, favorite: false, success: false }
  }
}

/**
 * Edit an analysis view
 */
export const editAnalysisView = async (
  viewId: number,
  data: { name?: string; description?: string }
): Promise<{ id: number; success: boolean }> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.editAnalysisView(viewId, data)
    }

    return await apiClient.put<{ id: number; success: boolean }>(
      `/analysis-views/${viewId}/`,
      data
    )
  } catch (error) {
    console.error(`Error editing analysis ${viewId}:`, error)
    // Return a failure response
    return { id: viewId, success: false }
  }
}

/**
 * Delete an analysis view
 */
export const deleteAnalysisView = async (
  viewId: number
): Promise<{ success: boolean; message?: string }> => {
  try {
    if (API_CONFIG.USE_MOCK_DATA) {
      return await mockService.deleteAnalysisView(viewId)
    }

    return await apiClient.delete<{ success: boolean; message?: string }>(
      `/analysis-views/${viewId}/delete/`
    )
  } catch (error) {
    console.error(`Error deleting analysis ${viewId}:`, error)
    // Return a failure response
    return { success: false, message: "Failed to delete analysis view" }
  }
}
