"use client"

import React from "react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    title: "Chief Financial Officer",
    department: "Executive",
    email: "emily.<PERSON><PERSON><PERSON><PERSON>@leviathan.com",
    avatar: "👩‍💼",
    recentAnalyses: 3,
  },
  {
    id: 2,
    name: "<PERSON>",
    title: "VP of Financial Planning",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👨‍💼",
    recentAnalyses: 5,
  },
  {
    id: 3,
    name: "<PERSON>",
    title: "Financial Analyst",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👩‍💻",
    recentAnalyses: 8,
  },
  {
    id: 4,
    name: "<PERSON>",
    title: "Data Scientist",
    department: "Analytics",
    email: "<EMAIL>",
    avatar: "👨‍💻",
    recentAnalyses: 6,
  },
  {
    id: 5,
    name: "<PERSON>",
    title: "Business Intelligence Manager",
    department: "Analytics",
    email: "<EMAIL>",
    avatar: "👩‍💼",
    recentAnalyses: 4,
  },
  {
    id: 6,
    name: "<PERSON>",
    title: "Controller",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👨‍💼",
    recentAnalyses: 2,
  },
]

const departments = [
  { name: "All", count: teamMembers.length },
  {
    name: "Executive",
    count: teamMembers.filter((m) => m.department === "Executive").length,
  },
  {
    name: "Finance",
    count: teamMembers.filter((m) => m.department === "Finance").length,
  },
  {
    name: "Analytics",
    count: teamMembers.filter((m) => m.department === "Analytics").length,
  },
]

const Team = () => {
  return (
    <>
      <Breadcrumb links={[{ label: "Team" }]} />

      <UnderConstructionAlert />

      <main className="flex-grow overflow-auto">
        <div>
          <h1 className="text-primary border-primary mb-6 border-b pb-2 text-2xl font-semibold">
            Team
          </h1>

          <div className="mb-6 rounded-lg bg-white p-6 shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Team Members
                </h2>
                <p className="mt-1 text-sm text-gray-600">
                  Financial team members with access to the Leviathan platform.
                </p>
              </div>
              <div className="flex space-x-2">
                <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                  Invite Member
                </button>
                <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                  Manage Permissions
                </button>
              </div>
            </div>

            {/* Department filters */}
            <div className="mb-4 flex space-x-3">
              {departments.map((dept, index) => (
                <button
                  key={index}
                  className={`rounded px-3 py-1.5 text-xs font-medium ${
                    index === 0
                      ? "bg-gray-200 text-gray-800"
                      : "border border-gray-300 bg-white text-gray-600 hover:bg-gray-50"
                  }`}
                >
                  {dept.name} ({dept.count})
                </button>
              ))}
            </div>

            {/* Team members table */}
            <div className="overflow-hidden rounded border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Team Member
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Title
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Department
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Recent Activity
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {teamMembers.map((member) => (
                    <tr key={member.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {member.name}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.title}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.department}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.recentAnalyses} recent analyses
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right text-sm font-medium">
                        <button className="mr-3 text-gray-600 hover:text-gray-900">
                          View
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Team activity */}
          <div className="rounded-lg bg-white p-6 shadow-md">
            <h2 className="mb-4 text-lg font-semibold text-gray-900">
              Recent Team Activity
            </h2>

            <div className="space-y-4">
              <div className="border-l-2 border-gray-300 py-2 pl-4">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">Sophia Chen</span> shared a
                      new analysis
                    </p>
                    <p className="mt-1 text-xs text-gray-500">2 hours ago</p>
                  </div>
                </div>
                <p className="mt-2 text-sm text-gray-600">
                  {`I've`} completed the Q1 profitability analysis with some
                  interesting findings on our new product line.
                </p>
                <div className="mt-2 rounded border border-gray-200 bg-gray-50 p-3 text-sm">
                  <div className="flex items-center text-gray-800">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="mr-2 h-4 w-4 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    Q1 2025 Product Line Profitability Analysis
                  </div>
                </div>
              </div>

              <div className="border-l-2 border-gray-300 py-2 pl-4">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">David Kim</span> commented
                      on an analysis
                    </p>
                    <p className="mt-1 text-xs text-gray-500">Yesterday</p>
                  </div>
                </div>
                <p className="mt-2 text-sm text-gray-600">
                  Great work on the cash flow projections. {`I've`} added some
                  notes on the seasonal variations we should consider for Q3.
                </p>
              </div>

              <div className="border-l-2 border-gray-300 py-2 pl-4">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">Emily Rodriguez</span>{" "}
                      updated permissions for 3 team members
                    </p>
                    <p className="mt-1 text-xs text-gray-500">2 days ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default Team
