{"version": 3, "sources": ["../../src/build/progress.ts"], "sourcesContent": ["import * as Log from '../build/output/log'\nimport createSpinner from './spinner'\n\nfunction divideSegments(number: number, segments: number): number[] {\n  const result = []\n  while (number > 0 && segments > 0) {\n    const dividedNumber =\n      number < segments ? number : Math.floor(number / segments)\n\n    number -= dividedNumber\n    segments--\n    result.push(dividedNumber)\n  }\n  return result\n}\n\nexport const createProgress = (total: number, label: string) => {\n  const segments = divideSegments(total, 4)\n\n  if (total === 0) {\n    throw new Error('invariant: progress total can not be zero')\n  }\n  let currentSegmentTotal = segments.shift()\n  let currentSegmentCount = 0\n  let lastProgressOutput = Date.now()\n  let curProgress = 0\n  let progressSpinner = createSpinner(`${label} (${curProgress}/${total})`, {\n    spinner: {\n      frames: [\n        '[    ]',\n        '[=   ]',\n        '[==  ]',\n        '[=== ]',\n        '[ ===]',\n        '[  ==]',\n        '[   =]',\n        '[    ]',\n        '[   =]',\n        '[  ==]',\n        '[ ===]',\n        '[====]',\n        '[=== ]',\n        '[==  ]',\n        '[=   ]',\n      ],\n      interval: 200,\n    },\n  })\n\n  const run = () => {\n    curProgress++\n\n    // Make sure we only log once\n    // - per fully generated segment, or\n    // - per minute\n    // when not showing the spinner\n    if (!progressSpinner) {\n      currentSegmentCount++\n\n      if (currentSegmentCount === currentSegmentTotal) {\n        currentSegmentTotal = segments.shift()\n        currentSegmentCount = 0\n      } else if (lastProgressOutput + 60000 > Date.now()) {\n        return\n      }\n\n      lastProgressOutput = Date.now()\n    }\n\n    const isFinished = curProgress === total\n    const message = `${label} (${curProgress}/${total})`\n    if (progressSpinner && !isFinished) {\n      progressSpinner.setText(message)\n    } else {\n      progressSpinner?.stop()\n      if (isFinished) {\n        Log.event(message)\n      } else {\n        Log.info(`${message} ${process.stdout.isTTY ? '\\n' : '\\r'}`)\n      }\n    }\n  }\n\n  const clear = () => {\n    if (\n      progressSpinner &&\n      // Ensure only reset and clear once to avoid set operation overflow in ora\n      progressSpinner.isSpinning\n    ) {\n      progressSpinner.prefixText = '\\r'\n      progressSpinner.text = '\\r'\n      progressSpinner.clear()\n      progressSpinner.stop()\n    }\n  }\n\n  return {\n    run,\n    clear,\n  }\n}\n"], "names": ["createProgress", "divideSegments", "number", "segments", "result", "dividedNumber", "Math", "floor", "push", "total", "label", "Error", "currentSegmentTotal", "shift", "currentSegmentCount", "lastProgressOutput", "Date", "now", "curProgress", "progressSpinner", "createSpinner", "spinner", "frames", "interval", "run", "isFinished", "message", "setText", "stop", "Log", "event", "info", "process", "stdout", "isTTY", "clear", "isSpinning", "prefixText", "text"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;6DAhBQ;gEACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,SAASC,eAAeC,MAAc,EAAEC,QAAgB;IACtD,MAAMC,SAAS,EAAE;IACjB,MAAOF,SAAS,KAAKC,WAAW,EAAG;QACjC,MAAME,gBACJH,SAASC,WAAWD,SAASI,KAAKC,KAAK,CAACL,SAASC;QAEnDD,UAAUG;QACVF;QACAC,OAAOI,IAAI,CAACH;IACd;IACA,OAAOD;AACT;AAEO,MAAMJ,iBAAiB,CAACS,OAAeC;IAC5C,MAAMP,WAAWF,eAAeQ,OAAO;IAEvC,IAAIA,UAAU,GAAG;QACf,MAAM,qBAAsD,CAAtD,IAAIE,MAAM,8CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqD;IAC7D;IACA,IAAIC,sBAAsBT,SAASU,KAAK;IACxC,IAAIC,sBAAsB;IAC1B,IAAIC,qBAAqBC,KAAKC,GAAG;IACjC,IAAIC,cAAc;IAClB,IAAIC,kBAAkBC,IAAAA,gBAAa,EAAC,GAAGV,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC,EAAE;QACxEY,SAAS;YACPC,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACDC,UAAU;QACZ;IACF;IAEA,MAAMC,MAAM;QACVN;QAEA,6BAA6B;QAC7B,oCAAoC;QACpC,eAAe;QACf,+BAA+B;QAC/B,IAAI,CAACC,iBAAiB;YACpBL;YAEA,IAAIA,wBAAwBF,qBAAqB;gBAC/CA,sBAAsBT,SAASU,KAAK;gBACpCC,sBAAsB;YACxB,OAAO,IAAIC,qBAAqB,QAAQC,KAAKC,GAAG,IAAI;gBAClD;YACF;YAEAF,qBAAqBC,KAAKC,GAAG;QAC/B;QAEA,MAAMQ,aAAaP,gBAAgBT;QACnC,MAAMiB,UAAU,GAAGhB,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC;QACpD,IAAIU,mBAAmB,CAACM,YAAY;YAClCN,gBAAgBQ,OAAO,CAACD;QAC1B,OAAO;YACLP,mCAAAA,gBAAiBS,IAAI;YACrB,IAAIH,YAAY;gBACdI,KAAIC,KAAK,CAACJ;YACZ,OAAO;gBACLG,KAAIE,IAAI,CAAC,GAAGL,QAAQ,CAAC,EAAEM,QAAQC,MAAM,CAACC,KAAK,GAAG,OAAO,MAAM;YAC7D;QACF;IACF;IAEA,MAAMC,QAAQ;QACZ,IACEhB,mBACA,0EAA0E;QAC1EA,gBAAgBiB,UAAU,EAC1B;YACAjB,gBAAgBkB,UAAU,GAAG;YAC7BlB,gBAAgBmB,IAAI,GAAG;YACvBnB,gBAAgBgB,KAAK;YACrBhB,gBAAgBS,IAAI;QACtB;IACF;IAEA,OAAO;QACLJ;QACAW;IACF;AACF"}