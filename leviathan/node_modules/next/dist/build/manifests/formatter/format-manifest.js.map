{"version": 3, "sources": ["../../../../src/build/manifests/formatter/format-manifest.ts"], "sourcesContent": ["/**\n * Formats the manifest depending on the environment variable\n * `NODE_ENV`. If it's set to `development`, it will return a pretty printed\n * JSON string, otherwise it will return a minified JSON string.\n */\nexport function formatManifest<T extends object>(manifest: T): string {\n  return JSON.stringify(manifest, null, 2)\n}\n"], "names": ["formatManifest", "manifest", "JSON", "stringify"], "mappings": "AAAA;;;;CAIC;;;;+BACeA;;;eAAAA;;;AAAT,SAASA,eAAiCC,QAAW;IAC1D,OAAOC,KAAKC,SAAS,CAACF,UAAU,MAAM;AACxC"}