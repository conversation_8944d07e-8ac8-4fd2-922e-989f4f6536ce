{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "sourcesContent": ["import path from 'path'\nimport { warn } from '../build/output/log'\nimport { detectTypo } from './detect-typo'\nimport { realpathSync } from './realpath'\nimport { printAndExit } from '../server/lib/utils'\n\nexport function getProjectDir(dir?: string, exitOnEnoent = true) {\n  const resolvedDir = path.resolve(dir || '.')\n  try {\n    const realDir = realpathSync(resolvedDir)\n\n    if (\n      resolvedDir !== realDir &&\n      resolvedDir.toLowerCase() === realDir.toLowerCase()\n    ) {\n      warn(\n        `Invalid casing detected for project dir, received ${resolvedDir} actual path ${realDir}, see more info here https://nextjs.org/docs/messages/invalid-project-dir-casing`\n      )\n    }\n\n    return realDir\n  } catch (err: any) {\n    if (err.code === 'ENOENT' && exitOnEnoent) {\n      if (typeof dir === 'string') {\n        const detectedTypo = detectTypo(dir, [\n          'build',\n          'dev',\n          'info',\n          'lint',\n          'start',\n          'telemetry',\n          'experimental-test',\n        ])\n\n        if (detectedTypo) {\n          return printAndExit(\n            `\"next ${dir}\" does not exist. Did you mean \"next ${detectedTypo}\"?`\n          )\n        }\n      }\n\n      return printAndExit(\n        `Invalid project directory provided, no such directory: ${resolvedDir}`\n      )\n    }\n    throw err\n  }\n}\n"], "names": ["getProjectDir", "dir", "exitOnEnoent", "resolvedDir", "path", "resolve", "realDir", "realpathSync", "toLowerCase", "warn", "err", "code", "detectedTypo", "detectTypo", "printAndExit"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;6DANC;qBACI;4BACM;0BACE;uBACA;;;;;;AAEtB,SAASA,cAAcC,GAAY,EAAEC,eAAe,IAAI;IAC7D,MAAMC,cAAcC,aAAI,CAACC,OAAO,CAACJ,OAAO;IACxC,IAAI;QACF,MAAMK,UAAUC,IAAAA,sBAAY,EAACJ;QAE7B,IACEA,gBAAgBG,WAChBH,YAAYK,WAAW,OAAOF,QAAQE,WAAW,IACjD;YACAC,IAAAA,SAAI,EACF,CAAC,kDAAkD,EAAEN,YAAY,aAAa,EAAEG,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOI,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,YAAYT,cAAc;YACzC,IAAI,OAAOD,QAAQ,UAAU;gBAC3B,MAAMW,eAAeC,IAAAA,sBAAU,EAACZ,KAAK;oBACnC;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,IAAIW,cAAc;oBAChB,OAAOE,IAAAA,mBAAY,EACjB,CAAC,MAAM,EAAEb,IAAI,qCAAqC,EAAEW,aAAa,EAAE,CAAC;gBAExE;YACF;YAEA,OAAOE,IAAAA,mBAAY,EACjB,CAAC,uDAAuD,EAAEX,aAAa;QAE3E;QACA,MAAMO;IACR;AACF"}