{"version": 3, "sources": ["../../src/lib/format-cli-help-output.ts"], "sourcesContent": ["import type { Command, Help } from 'next/dist/compiled/commander'\nimport { bold } from '../lib/picocolors'\n\n// Copy-pasted from Commander's Help class -> formatHelp().\n// TL;DR, we're overriding the built-in help to add a few niceties.\n// Link: https://github.com/tj/commander.js/blob/master/lib/help.js\nconst formatCliHelpOutput = (cmd: Command, helper: Help) => {\n  const termWidth = helper.padWidth(cmd, helper)\n  const helpWidth = helper.helpWidth || 80\n  const itemIndentWidth = 2\n  const itemSeparatorWidth = 2 // between term and description\n\n  function formatItem(term: string, description: string) {\n    let value = term\n\n    if (description) {\n      if (term === 'directory') {\n        value = `[${term}]`\n      }\n\n      const fullText = `${value.padEnd(\n        termWidth + itemSeparatorWidth\n      )}${description}`\n\n      return helper.wrap(\n        fullText,\n        helpWidth - itemIndentWidth,\n        termWidth + itemSeparatorWidth\n      )\n    }\n\n    return term\n  }\n\n  function formatList(textArray: string[]) {\n    return textArray.join('\\n').replace(/^/gm, ' '.repeat(itemIndentWidth))\n  }\n\n  // Usage\n  let output = [`${bold('Usage:')} ${helper.commandUsage(cmd)}`, '']\n\n  // Description\n  const commandDescription = helper.commandDescription(cmd)\n\n  if (commandDescription.length > 0) {\n    output = output.concat([helper.wrap(commandDescription, helpWidth, 0), ''])\n  }\n\n  // Arguments\n  const argumentList = helper.visibleArguments(cmd).map((argument) => {\n    return formatItem(\n      helper.argumentTerm(argument),\n      helper.argumentDescription(argument)\n    )\n  })\n\n  if (argumentList.length > 0) {\n    output = output.concat([\n      `${bold('Arguments:')}`,\n      formatList(argumentList),\n      '',\n    ])\n  }\n\n  // Options\n  const optionList = helper.visibleOptions(cmd).map((option) => {\n    return formatItem(\n      helper.optionTerm(option),\n      helper.optionDescription(option)\n    )\n  })\n\n  if (optionList.length > 0) {\n    output = output.concat([`${bold('Options:')}`, formatList(optionList), ''])\n  }\n\n  // Commands\n  const commandList = helper.visibleCommands(cmd).map((subCmd) => {\n    return formatItem(\n      helper.subcommandTerm(subCmd),\n      helper.subcommandDescription(subCmd)\n    )\n  })\n\n  if (commandList.length > 0) {\n    output = output.concat([\n      `${bold('Commands:')}`,\n      formatList(commandList),\n      '',\n    ])\n  }\n\n  return output.join('\\n')\n}\n\nexport { formatCliHelpOutput }\n"], "names": ["formatCliHelpOutput", "cmd", "helper", "termWidth", "<PERSON><PERSON><PERSON><PERSON>", "helpWidth", "itemIndentWidth", "itemSeparatorWidth", "formatItem", "term", "description", "value", "fullText", "padEnd", "wrap", "formatList", "textArray", "join", "replace", "repeat", "output", "bold", "commandUsage", "commandDescription", "length", "concat", "argumentList", "visibleArguments", "map", "argument", "argumentTerm", "argumentDescription", "optionList", "visibleOptions", "option", "optionTerm", "optionDescription", "commandList", "visibleCommands", "subCmd", "subcommandTerm", "subcommandDescription"], "mappings": ";;;;+BA+FSA;;;eAAAA;;;4BA9FY;AAErB,2DAA2D;AAC3D,mEAAmE;AACnE,mEAAmE;AACnE,MAAMA,sBAAsB,CAACC,KAAcC;IACzC,MAAMC,YAAYD,OAAOE,QAAQ,CAACH,KAAKC;IACvC,MAAMG,YAAYH,OAAOG,SAAS,IAAI;IACtC,MAAMC,kBAAkB;IACxB,MAAMC,qBAAqB,EAAE,+BAA+B;;IAE5D,SAASC,WAAWC,IAAY,EAAEC,WAAmB;QACnD,IAAIC,QAAQF;QAEZ,IAAIC,aAAa;YACf,IAAID,SAAS,aAAa;gBACxBE,QAAQ,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;YACrB;YAEA,MAAMG,WAAW,GAAGD,MAAME,MAAM,CAC9BV,YAAYI,sBACVG,aAAa;YAEjB,OAAOR,OAAOY,IAAI,CAChBF,UACAP,YAAYC,iBACZH,YAAYI;QAEhB;QAEA,OAAOE;IACT;IAEA,SAASM,WAAWC,SAAmB;QACrC,OAAOA,UAAUC,IAAI,CAAC,MAAMC,OAAO,CAAC,OAAO,IAAIC,MAAM,CAACb;IACxD;IAEA,QAAQ;IACR,IAAIc,SAAS;QAAC,GAAGC,IAAAA,gBAAI,EAAC,UAAU,CAAC,EAAEnB,OAAOoB,YAAY,CAACrB,MAAM;QAAE;KAAG;IAElE,cAAc;IACd,MAAMsB,qBAAqBrB,OAAOqB,kBAAkB,CAACtB;IAErD,IAAIsB,mBAAmBC,MAAM,GAAG,GAAG;QACjCJ,SAASA,OAAOK,MAAM,CAAC;YAACvB,OAAOY,IAAI,CAACS,oBAAoBlB,WAAW;YAAI;SAAG;IAC5E;IAEA,YAAY;IACZ,MAAMqB,eAAexB,OAAOyB,gBAAgB,CAAC1B,KAAK2B,GAAG,CAAC,CAACC;QACrD,OAAOrB,WACLN,OAAO4B,YAAY,CAACD,WACpB3B,OAAO6B,mBAAmB,CAACF;IAE/B;IAEA,IAAIH,aAAaF,MAAM,GAAG,GAAG;QAC3BJ,SAASA,OAAOK,MAAM,CAAC;YACrB,GAAGJ,IAAAA,gBAAI,EAAC,eAAe;YACvBN,WAAWW;YACX;SACD;IACH;IAEA,UAAU;IACV,MAAMM,aAAa9B,OAAO+B,cAAc,CAAChC,KAAK2B,GAAG,CAAC,CAACM;QACjD,OAAO1B,WACLN,OAAOiC,UAAU,CAACD,SAClBhC,OAAOkC,iBAAiB,CAACF;IAE7B;IAEA,IAAIF,WAAWR,MAAM,GAAG,GAAG;QACzBJ,SAASA,OAAOK,MAAM,CAAC;YAAC,GAAGJ,IAAAA,gBAAI,EAAC,aAAa;YAAEN,WAAWiB;YAAa;SAAG;IAC5E;IAEA,WAAW;IACX,MAAMK,cAAcnC,OAAOoC,eAAe,CAACrC,KAAK2B,GAAG,CAAC,CAACW;QACnD,OAAO/B,WACLN,OAAOsC,cAAc,CAACD,SACtBrC,OAAOuC,qBAAqB,CAACF;IAEjC;IAEA,IAAIF,YAAYb,MAAM,GAAG,GAAG;QAC1BJ,SAASA,OAAOK,MAAM,CAAC;YACrB,GAAGJ,IAAAA,gBAAI,EAAC,cAAc;YACtBN,WAAWsB;YACX;SACD;IACH;IAEA,OAAOjB,OAAOH,IAAI,CAAC;AACrB"}