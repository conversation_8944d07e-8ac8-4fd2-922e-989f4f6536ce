{"version": 3, "sources": ["../../src/lib/pretty-bytes.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst UNITS = ['B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/\nconst toLocaleString = (number: number, locale: any) => {\n  let result: any = number\n  if (typeof locale === 'string') {\n    result = number.toLocaleString(locale)\n  } else if (locale === true) {\n    result = number.toLocaleString()\n  }\n\n  return result\n}\n\nexport default function prettyBytes(number: number, options?: any): string {\n  if (!Number.isFinite(number)) {\n    throw new TypeError(\n      `Expected a finite number, got ${typeof number}: ${number}`\n    )\n  }\n\n  options = Object.assign({}, options)\n\n  if (options.signed && number === 0) {\n    return ' 0 B'\n  }\n\n  const isNegative = number < 0\n  const prefix = isNegative ? '-' : options.signed ? '+' : ''\n\n  if (isNegative) {\n    number = -number\n  }\n\n  if (number < 1) {\n    const numberString = toLocaleString(number, options.locale)\n    return prefix + numberString + ' B'\n  }\n\n  const exponent = Math.min(\n    Math.floor(Math.log10(number) / 3),\n    UNITS.length - 1\n  )\n\n  number = Number((number / Math.pow(1000, exponent)).toPrecision(3))\n  const numberString = toLocaleString(number, options.locale)\n\n  const unit = UNITS[exponent]\n\n  return prefix + numberString + ' ' + unit\n}\n"], "names": ["prettyBytes", "UNITS", "toLocaleString", "number", "locale", "result", "options", "Number", "isFinite", "TypeError", "Object", "assign", "signed", "isNegative", "prefix", "numberString", "exponent", "Math", "min", "floor", "log10", "length", "pow", "toPrecision", "unit"], "mappings": "AAAA;;;;;;;;;;AAUA;;;;+BAqBA;;;eAAwBA;;;AAnBxB,MAAMC,QAAQ;IAAC;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CAAK;AAEnE;;;;;AAKA,GACA,MAAMC,iBAAiB,CAACC,QAAgBC;IACtC,IAAIC,SAAcF;IAClB,IAAI,OAAOC,WAAW,UAAU;QAC9BC,SAASF,OAAOD,cAAc,CAACE;IACjC,OAAO,IAAIA,WAAW,MAAM;QAC1BC,SAASF,OAAOD,cAAc;IAChC;IAEA,OAAOG;AACT;AAEe,SAASL,YAAYG,MAAc,EAAEG,OAAa;IAC/D,IAAI,CAACC,OAAOC,QAAQ,CAACL,SAAS;QAC5B,MAAM,qBAEL,CAFK,IAAIM,UACR,CAAC,8BAA8B,EAAE,OAAON,OAAO,EAAE,EAAEA,QAAQ,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAG,UAAUI,OAAOC,MAAM,CAAC,CAAC,GAAGL;IAE5B,IAAIA,QAAQM,MAAM,IAAIT,WAAW,GAAG;QAClC,OAAO;IACT;IAEA,MAAMU,aAAaV,SAAS;IAC5B,MAAMW,SAASD,aAAa,MAAMP,QAAQM,MAAM,GAAG,MAAM;IAEzD,IAAIC,YAAY;QACdV,SAAS,CAACA;IACZ;IAEA,IAAIA,SAAS,GAAG;QACd,MAAMY,eAAeb,eAAeC,QAAQG,QAAQF,MAAM;QAC1D,OAAOU,SAASC,eAAe;IACjC;IAEA,MAAMC,WAAWC,KAAKC,GAAG,CACvBD,KAAKE,KAAK,CAACF,KAAKG,KAAK,CAACjB,UAAU,IAChCF,MAAMoB,MAAM,GAAG;IAGjBlB,SAASI,OAAO,AAACJ,CAAAA,SAASc,KAAKK,GAAG,CAAC,MAAMN,SAAQ,EAAGO,WAAW,CAAC;IAChE,MAAMR,eAAeb,eAAeC,QAAQG,QAAQF,MAAM;IAE1D,MAAMoB,OAAOvB,KAAK,CAACe,SAAS;IAE5B,OAAOF,SAASC,eAAe,MAAMS;AACvC"}