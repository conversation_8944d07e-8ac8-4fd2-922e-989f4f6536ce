{"version": 3, "sources": ["../../src/cli/next-telemetry.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { bold, cyan, green, red, yellow } from '../lib/picocolors'\nimport { Telemetry } from '../telemetry/storage'\n\nexport type NextTelemetryOptions = {\n  enable?: boolean\n  disable?: boolean\n}\n\nconst telemetry = new Telemetry({ distDir: process.cwd() })\nlet isEnabled = telemetry.isEnabled\n\nconst nextTelemetry = (options: NextTelemetryOptions, arg: string) => {\n  if (options.enable || arg === 'enable') {\n    telemetry.setEnabled(true)\n    isEnabled = true\n\n    console.log(cyan('Success!'))\n  } else if (options.disable || arg === 'disable') {\n    const path = telemetry.setEnabled(false)\n\n    if (isEnabled) {\n      console.log(\n        cyan(`Your preference has been saved${path ? ` to ${path}` : ''}.`)\n      )\n    } else {\n      console.log(yellow(`Next.js' telemetry collection is already disabled.`))\n    }\n\n    isEnabled = false\n  } else {\n    console.log(bold('Next.js Telemetry'))\n  }\n\n  console.log(\n    `\\nStatus: ${isEnabled ? bold(green('Enabled')) : bold(red('Disabled'))}`\n  )\n\n  if (isEnabled) {\n    console.log(\n      '\\nNext.js telemetry is completely anonymous. Thank you for participating!'\n    )\n  } else {\n    console.log(\n      `\\nYou have opted-out of Next.js' anonymous telemetry program.\\nNo data will be collected from your machine.`\n    )\n  }\n\n  console.log(`\\nLearn more: ${cyan('https://nextjs.org/telemetry')}`)\n}\n\nexport { nextTelemetry }\n"], "names": ["nextTelemetry", "telemetry", "Telemetry", "distDir", "process", "cwd", "isEnabled", "options", "arg", "enable", "setEnabled", "console", "log", "cyan", "disable", "path", "yellow", "bold", "green", "red"], "mappings": ";;;;;+BAoDSA;;;eAAAA;;;4BAlDsC;yBACrB;AAO1B,MAAMC,YAAY,IAAIC,kBAAS,CAAC;IAAEC,SAASC,QAAQC,GAAG;AAAG;AACzD,IAAIC,YAAYL,UAAUK,SAAS;AAEnC,MAAMN,gBAAgB,CAACO,SAA+BC;IACpD,IAAID,QAAQE,MAAM,IAAID,QAAQ,UAAU;QACtCP,UAAUS,UAAU,CAAC;QACrBJ,YAAY;QAEZK,QAAQC,GAAG,CAACC,IAAAA,gBAAI,EAAC;IACnB,OAAO,IAAIN,QAAQO,OAAO,IAAIN,QAAQ,WAAW;QAC/C,MAAMO,OAAOd,UAAUS,UAAU,CAAC;QAElC,IAAIJ,WAAW;YACbK,QAAQC,GAAG,CACTC,IAAAA,gBAAI,EAAC,CAAC,8BAA8B,EAAEE,OAAO,CAAC,IAAI,EAAEA,MAAM,GAAG,GAAG,CAAC,CAAC;QAEtE,OAAO;YACLJ,QAAQC,GAAG,CAACI,IAAAA,kBAAM,EAAC,CAAC,kDAAkD,CAAC;QACzE;QAEAV,YAAY;IACd,OAAO;QACLK,QAAQC,GAAG,CAACK,IAAAA,gBAAI,EAAC;IACnB;IAEAN,QAAQC,GAAG,CACT,CAAC,UAAU,EAAEN,YAAYW,IAAAA,gBAAI,EAACC,IAAAA,iBAAK,EAAC,cAAcD,IAAAA,gBAAI,EAACE,IAAAA,eAAG,EAAC,cAAc;IAG3E,IAAIb,WAAW;QACbK,QAAQC,GAAG,CACT;IAEJ,OAAO;QACLD,QAAQC,GAAG,CACT,CAAC,2GAA2G,CAAC;IAEjH;IAEAD,QAAQC,GAAG,CAAC,CAAC,cAAc,EAAEC,IAAAA,gBAAI,EAAC,iCAAiC;AACrE"}