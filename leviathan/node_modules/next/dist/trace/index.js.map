{"version": 3, "sources": ["../../src/trace/index.ts"], "sourcesContent": ["import {\n  trace,\n  exportTraceState,\n  flushAllTraces,\n  getTraceEvents,\n  initializeTraceState,\n  recordTraceEvents,\n  Span,\n  SpanStatus,\n} from './trace'\nimport { setGlobal } from './shared'\nimport type { SpanId, TraceEvent, TraceState } from './types'\n\nexport {\n  trace,\n  exportTraceState,\n  flushAllTraces,\n  getTraceEvents,\n  initializeTraceState,\n  recordTraceEvents,\n  Span,\n  setGlobal,\n  SpanStatus,\n}\nexport type { SpanId, TraceEvent, TraceState }\n"], "names": ["Span", "SpanStatus", "exportTraceState", "flushAllTraces", "getTraceEvents", "initializeTraceState", "recordTraceEvents", "setGlobal", "trace"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAoBEA,IAAI;eAAJA,WAAI;;IAEJC,UAAU;eAAVA,iBAAU;;IAPVC,gBAAgB;eAAhBA,uBAAgB;;IAChBC,cAAc;eAAdA,qBAAc;;IACdC,cAAc;eAAdA,qBAAc;;IACdC,oBAAoB;eAApBA,2BAAoB;;IACpBC,iBAAiB;eAAjBA,wBAAiB;;IAEjBC,SAAS;eAATA,iBAAS;;IAPTC,KAAK;eAALA,YAAK;;;uBALA;wBACmB"}