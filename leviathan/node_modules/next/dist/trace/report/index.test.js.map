{"version": 3, "sources": ["../../../src/trace/report/index.test.ts"], "sourcesContent": ["import { mkdtemp, readFile } from 'fs/promises'\nimport { reporter } from '.'\nimport { setGlobal } from '../shared'\nimport { join } from 'path'\nimport { tmpdir } from 'os'\n\nconst TRACE_EVENT = {\n  name: 'test-span',\n  duration: 321,\n  timestamp: Date.now(),\n  id: 127,\n  startTime: Date.now(),\n}\nconst WEBPACK_INVALIDATED_EVENT = {\n  name: 'webpack-invalidated',\n  duration: 100,\n  timestamp: Date.now(),\n  id: 112,\n  startTime: Date.now(),\n}\n\ndescribe('Trace Reporter', () => {\n  describe('JSON reporter', () => {\n    it('should write the trace events to JSON file', async () => {\n      const tmpDir = await mkdtemp(join(tmpdir(), 'json-reporter'))\n      setGlobal('distDir', tmpDir)\n      setGlobal('phase', 'anything')\n      reporter.report(TRACE_EVENT)\n      await reporter.flushAll()\n      const traceFilename = join(tmpDir, 'trace')\n      const traces = JSON.parse(await readFile(traceFilename, 'utf-8'))\n      expect(traces.length).toEqual(1)\n      expect(traces[0].name).toEqual('test-span')\n      expect(traces[0].id).toEqual(127)\n      expect(traces[0].duration).toEqual(321)\n      expect(traces[0].traceId).toBeDefined()\n    })\n  })\n\n  describe('Telemetry reporter', () => {\n    it('should record telemetry event', async () => {\n      const recordMock = jest.fn()\n      const telemetryMock = {\n        record: recordMock,\n      }\n      setGlobal('telemetry', telemetryMock)\n      // This should be ignored.\n      reporter.report(TRACE_EVENT)\n      expect(recordMock).toHaveBeenCalledTimes(0)\n      reporter.report(WEBPACK_INVALIDATED_EVENT)\n      expect(recordMock).toHaveBeenCalledTimes(1)\n      expect(recordMock).toHaveBeenCalledWith({\n        eventName: 'WEBPACK_INVALIDATED',\n        payload: {\n          durationInMicroseconds: 100,\n        },\n      })\n    })\n  })\n})\n"], "names": ["TRACE_EVENT", "name", "duration", "timestamp", "Date", "now", "id", "startTime", "WEBPACK_INVALIDATED_EVENT", "describe", "it", "tmpDir", "mkdtemp", "join", "tmpdir", "setGlobal", "reporter", "report", "flushAll", "traceFilename", "traces", "JSON", "parse", "readFile", "expect", "length", "toEqual", "traceId", "toBeDefined", "recordMock", "jest", "fn", "telemetryMock", "record", "toHaveBeenCalledTimes", "toHaveBeenCalledWith", "eventName", "payload", "durationInMicroseconds"], "mappings": ";;;;0BAAkC;kBACT;wBACC;sBACL;oBACE;AAEvB,MAAMA,cAAc;IAClBC,MAAM;IACNC,UAAU;IACVC,WAAWC,KAAKC,GAAG;IACnBC,IAAI;IACJC,WAAWH,KAAKC,GAAG;AACrB;AACA,MAAMG,4BAA4B;IAChCP,MAAM;IACNC,UAAU;IACVC,WAAWC,KAAKC,GAAG;IACnBC,IAAI;IACJC,WAAWH,KAAKC,GAAG;AACrB;AAEAI,SAAS,kBAAkB;IACzBA,SAAS,iBAAiB;QACxBC,GAAG,8CAA8C;YAC/C,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACC,IAAAA,UAAI,EAACC,IAAAA,UAAM,KAAI;YAC5CC,IAAAA,iBAAS,EAAC,WAAWJ;YACrBI,IAAAA,iBAAS,EAAC,SAAS;YACnBC,UAAQ,CAACC,MAAM,CAACjB;YAChB,MAAMgB,UAAQ,CAACE,QAAQ;YACvB,MAAMC,gBAAgBN,IAAAA,UAAI,EAACF,QAAQ;YACnC,MAAMS,SAASC,KAAKC,KAAK,CAAC,MAAMC,IAAAA,kBAAQ,EAACJ,eAAe;YACxDK,OAAOJ,OAAOK,MAAM,EAAEC,OAAO,CAAC;YAC9BF,OAAOJ,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEyB,OAAO,CAAC;YAC/BF,OAAOJ,MAAM,CAAC,EAAE,CAACd,EAAE,EAAEoB,OAAO,CAAC;YAC7BF,OAAOJ,MAAM,CAAC,EAAE,CAAClB,QAAQ,EAAEwB,OAAO,CAAC;YACnCF,OAAOJ,MAAM,CAAC,EAAE,CAACO,OAAO,EAAEC,WAAW;QACvC;IACF;IAEAnB,SAAS,sBAAsB;QAC7BC,GAAG,iCAAiC;YAClC,MAAMmB,aAAaC,KAAKC,EAAE;YAC1B,MAAMC,gBAAgB;gBACpBC,QAAQJ;YACV;YACAd,IAAAA,iBAAS,EAAC,aAAaiB;YACvB,0BAA0B;YAC1BhB,UAAQ,CAACC,MAAM,CAACjB;YAChBwB,OAAOK,YAAYK,qBAAqB,CAAC;YACzClB,UAAQ,CAACC,MAAM,CAACT;YAChBgB,OAAOK,YAAYK,qBAAqB,CAAC;YACzCV,OAAOK,YAAYM,oBAAoB,CAAC;gBACtCC,WAAW;gBACXC,SAAS;oBACPC,wBAAwB;gBAC1B;YACF;QACF;IACF;AACF"}