/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/team/page";
exports.ids = ["app/team/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6edaa2d14651\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zaGVuZy9EZXZlbG9wZXIvUmVsdXZhdGUvb25lZmxvdy9sZXZpYXRoYW4vYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmVkYWEyZDE0NjUxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _context_AppContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/AppContext */ \"(rsc)/./context/AppContext.tsx\");\n/* harmony import */ var _context_PendingActionsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/PendingActionsContext */ \"(rsc)/./context/PendingActionsContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Leviathan Analytics\",\n    description: \"Healthcare business intelligence platform\"\n};\nfunction RootLayout({ children }) {\n    // Initial filters for the application\n    const initialFilters = [\n        {\n            id: 1,\n            value: \"North America\",\n            color: \"#4F46E5\"\n        },\n        {\n            id: 2,\n            value: \"Q1-Q4 2024\",\n            color: \"#10B981\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AppContext__WEBPACK_IMPORTED_MODULE_1__.AppProvider, {\n                initialFilters: initialFilters,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PendingActionsContext__WEBPACK_IMPORTED_MODULE_2__.PendingActionsProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/layout.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/team/page.tsx":
/*!***************************!*\
  !*** ./app/team/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./context/AppContext.tsx":
/*!********************************!*\
  !*** ./context/AppContext.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProvider: () => (/* binding */ AppProvider),
/* harmony export */   useAppContext: () => (/* binding */ useAppContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AppProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/AppContext.tsx",
"AppProvider",
);const useAppContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAppContext() from the server but useAppContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/AppContext.tsx",
"useAppContext",
);

/***/ }),

/***/ "(rsc)/./context/PendingActionsContext.tsx":
/*!*******************************************!*\
  !*** ./context/PendingActionsContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PendingActionsProvider: () => (/* binding */ PendingActionsProvider),
/* harmony export */   usePendingActions: () => (/* binding */ usePendingActions)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const usePendingActions = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePendingActions() from the server but usePendingActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/PendingActionsContext.tsx",
"usePendingActions",
);const PendingActionsProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PendingActionsProvider() from the server but PendingActionsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/PendingActionsContext.tsx",
"PendingActionsProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fteam%2Fpage&page=%2Fteam%2Fpage&appPaths=%2Fteam%2Fpage&pagePath=private-next-app-dir%2Fteam%2Fpage.tsx&appDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fteam%2Fpage&page=%2Fteam%2Fpage&appPaths=%2Fteam%2Fpage&pagePath=private-next-app-dir%2Fteam%2Fpage.tsx&appDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/team/page.tsx */ \"(rsc)/./app/team/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'team',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/team/page\",\n        pathname: \"/team\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fteam%2Fpage&page=%2Fteam%2Fpage&appPaths=%2Fteam%2Fpage&pagePath=private-next-app-dir%2Fteam%2Fpage.tsx&appDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/team/page.tsx */ \"(rsc)/./app/team/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2hlbmclMkZEZXZlbG9wZXIlMkZSZWx1dmF0ZSUyRm9uZWZsb3clMkZsZXZpYXRoYW4lMkZhcHAlMkZ0ZWFtJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUF3RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoZW5nL0RldmVsb3Blci9SZWx1dmF0ZS9vbmVmbG93L2xldmlhdGhhbi9hcHAvdGVhbS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./context/AppContext.tsx */ \"(rsc)/./context/AppContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./context/PendingActionsContext.tsx */ \"(rsc)/./context/PendingActionsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"256x253\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiL1VzZXJzL3NoZW5nL0RldmVsb3Blci9SZWx1dmF0ZS9vbmVmbG93L2xldmlhdGhhbi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMjU2eDI1M1wifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/team/page.tsx":
/*!***************************!*\
  !*** ./app/team/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/layout/Sidebar */ \"(ssr)/./components/layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst TeamPage = ()=>{\n    // Sample team data\n    const teamMembers = [\n        {\n            id: 1,\n            name: 'Emily Rodriguez',\n            title: 'Chief Financial Officer',\n            department: 'Executive',\n            email: '<EMAIL>',\n            avatar: '👩‍💼',\n            recentAnalyses: 3\n        },\n        {\n            id: 2,\n            name: 'David Kim',\n            title: 'VP of Financial Planning',\n            department: 'Finance',\n            email: '<EMAIL>',\n            avatar: '👨‍💼',\n            recentAnalyses: 5\n        },\n        {\n            id: 3,\n            name: 'Sophia Chen',\n            title: 'Financial Analyst',\n            department: 'Finance',\n            email: '<EMAIL>',\n            avatar: '👩‍💻',\n            recentAnalyses: 8\n        },\n        {\n            id: 4,\n            name: 'Marcus Johnson',\n            title: 'Data Scientist',\n            department: 'Analytics',\n            email: '<EMAIL>',\n            avatar: '👨‍💻',\n            recentAnalyses: 6\n        },\n        {\n            id: 5,\n            name: 'Olivia Williams',\n            title: 'Business Intelligence Manager',\n            department: 'Analytics',\n            email: '<EMAIL>',\n            avatar: '👩‍💼',\n            recentAnalyses: 4\n        },\n        {\n            id: 6,\n            name: 'James Taylor',\n            title: 'Controller',\n            department: 'Finance',\n            email: '<EMAIL>',\n            avatar: '👨‍💼',\n            recentAnalyses: 2\n        }\n    ];\n    // Sample departments\n    const departments = [\n        {\n            name: 'All',\n            count: teamMembers.length\n        },\n        {\n            name: 'Executive',\n            count: teamMembers.filter((m)=>m.department === 'Executive').length\n        },\n        {\n            name: 'Finance',\n            count: teamMembers.filter((m)=>m.department === 'Finance').length\n        },\n        {\n            name: 'Analytics',\n            count: teamMembers.filter((m)=>m.department === 'Analytics').length\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow p-8 overflow-auto bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-6 text-[var(--navy)] border-b pb-2 border-[var(--turquoise)]\",\n                            children: \"Team\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-6 rounded-lg shadow-md mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Team Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: \"Financial team members with access to the Leviathan platform.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-xs font-medium text-gray-700 border border-gray-300 px-3 py-1.5 rounded hover:bg-gray-50\",\n                                                    children: \"Invite Member\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-xs font-medium text-gray-700 border border-gray-300 px-3 py-1.5 rounded hover:bg-gray-50\",\n                                                    children: \"Manage Permissions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 mb-4\",\n                                    children: departments.map((dept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: `px-3 py-1.5 rounded text-xs font-medium ${index === 0 ? 'bg-gray-200 text-gray-800' : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'}`,\n                                            children: [\n                                                dept.name,\n                                                \" (\",\n                                                dept.count,\n                                                \")\"\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden border border-gray-200 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full divide-y divide-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Team Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Title\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Recent Activity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"bg-white divide-y divide-gray-200\",\n                                                children: teamMembers.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: member.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                        lineNumber: 132,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: member.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: member.department\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        member.recentAnalyses,\n                                                                        \" recent analyses\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-gray-600 hover:text-gray-900 mr-3\",\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-gray-600 hover:text-gray-900\",\n                                                                        children: \"Edit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, member.id, true, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-6 rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Recent Team Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-l-2 border-gray-300 pl-4 py-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Sophia Chen\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 58\n                                                                    }, undefined),\n                                                                    \" shared a new analysis\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: \"2 hours ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-2\",\n                                                    children: \"I've completed the Q1 profitability analysis with some interesting findings on our new product line.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 bg-gray-50 p-3 rounded border border-gray-200 text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 mr-2 text-gray-500\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 1.5,\n                                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Q1 2025 Product Line Profitability Analysis\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-l-2 border-gray-300 pl-4 py-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"David Kim\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 58\n                                                                    }, undefined),\n                                                                    \" commented on an analysis\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: \"Yesterday\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mt-2\",\n                                                    children: \"Great work on the cash flow projections. I've added some notes on the seasonal variations we should consider for Q3.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-l-2 border-gray-300 pl-4 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Emily Rodriguez\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 58\n                                                                }, undefined),\n                                                                \" updated permissions for 3 team members\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"2 days ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/app/team/page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeamPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/team/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/Sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/mockData */ \"(ssr)/./data/mockData.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/api */ \"(ssr)/./services/api.ts\");\n/* harmony import */ var _modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../modals/DeleteConfirmationModal */ \"(ssr)/./components/modals/DeleteConfirmationModal.tsx\");\n/* harmony import */ var _modals_EditAnalysisModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../modals/EditAnalysisModal */ \"(ssr)/./components/modals/EditAnalysisModal.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_PendingActionsContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../context/PendingActionsContext */ \"(ssr)/./context/PendingActionsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst Sidebar = ({ analysisViews = _data_mockData__WEBPACK_IMPORTED_MODULE_2__.analysisViews, activeViewId = 1, onViewSelect = ()=>{} })=>{\n    // Get pending actions count from context\n    const { pendingActionsCount } = (0,_context_PendingActionsContext__WEBPACK_IMPORTED_MODULE_8__.usePendingActions)();\n    // Get current pathname for active link highlighting\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    // State for views with favorites sorted to the top\n    const [sortedViews, setSortedViews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(analysisViews);\n    // Track which parent items are hovered\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for modals\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editModalOpen, setEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAnalysis, setSelectedAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Sort views whenever analysisViews changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            // Sort the views to put favorites at the top\n            const sortViews = {\n                \"Sidebar.useEffect.sortViews\": (views)=>{\n                    return [\n                        ...views\n                    ].map({\n                        \"Sidebar.useEffect.sortViews\": (view)=>{\n                            if (view.children && view.children.length > 0) {\n                                // Sort children with favorites at the top\n                                const sortedChildren = [\n                                    ...view.children\n                                ].sort({\n                                    \"Sidebar.useEffect.sortViews.sortedChildren\": (a, b)=>{\n                                        if (a.favorite && !b.favorite) return -1;\n                                        if (!a.favorite && b.favorite) return 1;\n                                        return 0;\n                                    }\n                                }[\"Sidebar.useEffect.sortViews.sortedChildren\"]);\n                                return {\n                                    ...view,\n                                    children: sortedChildren\n                                };\n                            }\n                            return view;\n                        }\n                    }[\"Sidebar.useEffect.sortViews\"]);\n                }\n            }[\"Sidebar.useEffect.sortViews\"];\n            setSortedViews(sortViews(analysisViews));\n        }\n    }[\"Sidebar.useEffect\"], [\n        analysisViews\n    ]);\n    // Function to handle mouse enter on parent item\n    const handleMouseEnter = (viewId)=>{\n        if (hoveredItem !== viewId) {\n            setHoveredItem(viewId);\n        }\n    };\n    // Function to handle mouse leave\n    const handleMouseLeave = ()=>{\n        setHoveredItem(null);\n    };\n    // Function to toggle favorite status\n    const handleToggleFavorite = async (e, childId)=>{\n        e.stopPropagation(); // Prevent triggering the parent button click\n        try {\n            const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.toggleFavorite)(childId);\n            if (result.success) {\n                // The API service already updates the mock data, so we just need to re-sort\n                setSortedViews((prev)=>{\n                    return prev.map((view)=>{\n                        if (view.children && view.children.length > 0) {\n                            // Sort children with favorites at the top\n                            const sortedChildren = [\n                                ...view.children\n                            ].sort((a, b)=>{\n                                if (a.favorite && !b.favorite) return -1;\n                                if (!a.favorite && b.favorite) return 1;\n                                return 0;\n                            });\n                            return {\n                                ...view,\n                                children: sortedChildren\n                            };\n                        }\n                        return view;\n                    });\n                });\n            }\n        } catch (error) {\n            console.error('Error toggling favorite:', error);\n        }\n    };\n    // Function to open edit modal\n    const handleEditClick = (e, child)=>{\n        e.stopPropagation(); // Prevent triggering the parent button click\n        setSelectedAnalysis({\n            id: child.id,\n            name: child.name\n        });\n        setEditModalOpen(true);\n    };\n    // Function to save edited analysis\n    const handleSaveEdit = async (newName)=>{\n        if (selectedAnalysis) {\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.editAnalysisView)(selectedAnalysis.id, {\n                    name: newName\n                });\n                if (result.success) {\n                    // Close the modal\n                    setEditModalOpen(false);\n                    setSelectedAnalysis(null);\n                }\n            } catch (error) {\n                console.error('Error editing analysis:', error);\n            }\n        }\n    };\n    // Function to open delete confirmation modal\n    const handleDeleteClick = (e, child)=>{\n        e.stopPropagation(); // Prevent triggering the parent button click\n        e.preventDefault(); // Prevent any default browser behavior\n        // Set the selected analysis and open the modal\n        setSelectedAnalysis({\n            id: child.id,\n            name: child.name\n        });\n        setDeleteModalOpen(true);\n    };\n    // Function to confirm deletion\n    const handleConfirmDelete = async ()=>{\n        if (selectedAnalysis) {\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.deleteAnalysisView)(selectedAnalysis.id);\n                if (result.success) {\n                    // Close the modal\n                    setDeleteModalOpen(false);\n                    setSelectedAnalysis(null);\n                    // Update the UI to reflect the deletion\n                    setSortedViews((prev)=>{\n                        return prev.map((view)=>{\n                            if (view.children && view.children.length > 0) {\n                                // Filter out the deleted child\n                                const updatedChildren = view.children.filter((child)=>child.id !== selectedAnalysis.id);\n                                return {\n                                    ...view,\n                                    children: updatedChildren\n                                };\n                            }\n                            return view;\n                        });\n                    });\n                }\n            } catch (error) {\n                console.error('Error deleting analysis:', error);\n            }\n        }\n    };\n    // Function to close the delete modal\n    const handleCloseDeleteModal = ()=>{\n        setDeleteModalOpen(false);\n        setSelectedAnalysis(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modals_DeleteConfirmationModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: deleteModalOpen,\n                onClose: handleCloseDeleteModal,\n                onConfirm: handleConfirmDelete,\n                itemName: selectedAnalysis?.name || ''\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modals_EditAnalysisModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editModalOpen,\n                onClose: ()=>setEditModalOpen(false),\n                onSave: handleSaveEdit,\n                initialName: selectedAnalysis?.name || ''\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"leviathan-sidebar w-64 h-full overflow-y-auto flex flex-col ml-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-opacity-10 border-white flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold tracking-tight\",\n                            children: \"Leviathan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4 flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xs uppercase tracking-wider text-gray-300 mb-3 px-4 font-medium\",\n                                children: \"Analysis Views\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: sortedViews.map((view)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: ()=>view.children && view.children.length > 0 && handleMouseEnter(view.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pl-2 flex items-center\",\n                                                            children: view.children && view.children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 p-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `flex-grow text-left py-2 px-2 flex items-center leviathan-analysis-item ${view.id === activeViewId ? 'active' : ''}`,\n                                                            onClick: ()=>onViewSelect(view.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    view.health && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `w-2 h-2 rounded-full mr-2 opacity-80`,\n                                                                        style: {\n                                                                            backgroundColor: view.health === 'green' ? '#22c55e' : view.health === 'amber' ? '#f97316' : '#ef4444'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: view.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    view.children && view.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-xs text-gray-400\",\n                                                                        children: view.children.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                view.children && view.children.length > 0 && hoveredItem === view.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"fixed z-[99]\",\n                                                    style: {\n                                                        left: '240px',\n                                                        top: '0',\n                                                        width: '20px',\n                                                        height: '100%',\n                                                        pointerEvents: 'auto'\n                                                    },\n                                                    onMouseEnter: ()=>handleMouseEnter(view.id)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                view.children && view.children.length > 0 && hoveredItem === view.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"fixed z-[100] bg-[var(--navy)] rounded-md shadow-lg py-2 min-w-[280px]\",\n                                                    style: {\n                                                        left: '240px',\n                                                        top: 'auto',\n                                                        transform: 'translateY(-20px)' // Position it slightly higher for better alignment\n                                                    },\n                                                    onMouseEnter: ()=>handleMouseEnter(view.id),\n                                                    onMouseLeave: handleMouseLeave,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1 text-xs uppercase tracking-wider text-[var(--turquoise)] border-b border-[var(--turquoise)] border-opacity-30 mb-1\",\n                                                            children: \"Saved Analyses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                \" \",\n                                                                view.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `relative w-full text-left py-3 px-3 hover:bg-[var(--navy-light)] ${child.id === activeViewId ? 'bg-[var(--navy-light)] text-white' : 'text-gray-300'}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-start\",\n                                                                                children: [\n                                                                                    \" \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"flex-grow text-left flex flex-col\",\n                                                                                        onClick: ()=>onViewSelect(child.id),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm font-medium block text-left\",\n                                                                                                children: child.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                lineNumber: 280,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            child.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs text-gray-400 mt-2 block text-left\",\n                                                                                                children: [\n                                                                                                    \" \",\n                                                                                                    \"Created: \",\n                                                                                                    new Date(child.createdAt).toLocaleDateString('en-US', {\n                                                                                                        year: 'numeric',\n                                                                                                        month: 'short',\n                                                                                                        day: 'numeric',\n                                                                                                        hour: '2-digit',\n                                                                                                        minute: '2-digit'\n                                                                                                    })\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                lineNumber: 282,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                        lineNumber: 276,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                className: `text-sm p-1.5 rounded transition-colors duration-150 ${child.favorite ? 'text-yellow-400 hover:bg-yellow-900 hover:bg-opacity-30' : 'text-gray-500 hover:text-yellow-300 hover:bg-[var(--navy-lighter)]'}`,\n                                                                                                onClick: (e)=>handleToggleFavorite(e, child.id),\n                                                                                                title: child.favorite ? 'Remove from favorites' : 'Add to favorites',\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    className: \"h-4 w-4\",\n                                                                                                    fill: child.favorite ? 'currentColor' : 'none',\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 1.5,\n                                                                                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                        lineNumber: 304,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                    lineNumber: 303,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                lineNumber: 296,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                className: \"text-sm p-1.5 rounded text-gray-400 transition-colors duration-150 hover:text-white hover:bg-blue-700 hover:bg-opacity-30\",\n                                                                                                onClick: (e)=>handleEditClick(e, child),\n                                                                                                title: \"Edit analysis\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    className: \"h-4 w-4\",\n                                                                                                    fill: \"none\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 1.5,\n                                                                                                        d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                        lineNumber: 315,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                    lineNumber: 314,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                lineNumber: 309,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                className: \"text-sm p-1.5 rounded text-gray-400 transition-colors duration-150 hover:text-red-300 hover:bg-red-900 hover:bg-opacity-30\",\n                                                                                                onClick: (e)=>handleDeleteClick(e, child),\n                                                                                                title: \"Delete analysis\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    className: \"h-4 w-4\",\n                                                                                                    fill: \"none\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 1.5,\n                                                                                                        d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                        lineNumber: 326,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                    lineNumber: 325,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                                lineNumber: 320,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, child.id, false, {\n                                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, view.id, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto\",\n                        style: {\n                            marginTop: \"-10px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xs uppercase tracking-wider text-gray-300 mb-3 px-4 font-medium pt-4\",\n                                children: \"Navigation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/actions\",\n                                            className: `w-full text-left py-2 px-6 flex items-center relative ${pathname === '/actions' ? 'text-[var(--turquoise)] bg-[var(--navy-light)]' : 'text-gray-300'} hover:text-[var(--turquoise)] hover:bg-[var(--navy-light)] transition-all duration-200 group`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                pendingActionsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-[var(--turquoise)] text-white\",\n                                                    children: pendingActionsCount\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute left-0 top-0 h-full w-1 bg-[var(--turquoise)] scale-y-0 group-hover:scale-y-100 transition-transform duration-200 origin-top\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/knowledge-base\",\n                                            className: `w-full text-left py-2 px-6 flex items-center relative ${pathname === '/knowledge-base' ? 'text-[var(--turquoise)] bg-[var(--navy-light)]' : 'text-gray-300'} hover:text-[var(--turquoise)] hover:bg-[var(--navy-light)] transition-all duration-200 group`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Knowledge Base\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute left-0 top-0 h-full w-1 bg-[var(--turquoise)] scale-y-0 group-hover:scale-y-100 transition-transform duration-200 origin-top\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/integrations\",\n                                            className: `w-full text-left py-2 px-6 flex items-center relative ${pathname === '/integrations' ? 'text-[var(--turquoise)] bg-[var(--navy-light)]' : 'text-gray-300'} hover:text-[var(--turquoise)] hover:bg-[var(--navy-light)] transition-all duration-200 group`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Integrations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute left-0 top-0 h-full w-1 bg-[var(--turquoise)] scale-y-0 group-hover:scale-y-100 transition-transform duration-200 origin-top\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/team\",\n                                            className: `w-full text-left py-2 px-6 flex items-center relative ${pathname === '/team' ? 'text-[var(--turquoise)] bg-[var(--navy-light)]' : 'text-gray-300'} hover:text-[var(--turquoise)] hover:bg-[var(--navy-light)] transition-all duration-200 group`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute left-0 top-0 h-full w-1 bg-[var(--turquoise)] scale-y-0 group-hover:scale-y-100 transition-transform duration-200 origin-top\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/settings\",\n                                            className: `w-full text-left py-2 px-6 flex items-center relative ${pathname === '/settings' ? 'text-[var(--turquoise)] bg-[var(--navy-light)]' : 'text-gray-300'} hover:text-[var(--turquoise)] hover:bg-[var(--navy-light)] transition-all duration-200 group`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute left-0 top-0 h-full w-1 bg-[var(--turquoise)] scale-y-0 group-hover:scale-y-100 transition-transform duration-200 origin-top\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-opacity-10 border-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"leviathan-button-primary w-full py-2 px-4 rounded text-sm\",\n                                    children: \"New Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/layout/Sidebar.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/modals/DeleteConfirmationModal.tsx":
/*!*******************************************************!*\
  !*** ./components/modals/DeleteConfirmationModal.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, itemName })=>{\n    // Add keyboard event listener to handle Escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DeleteConfirmationModal.useEffect\": ()=>{\n            const handleEscape = {\n                \"DeleteConfirmationModal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isOpen) {\n                        onClose();\n                    }\n                }\n            }[\"DeleteConfirmationModal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n                // Prevent scrolling when modal is open\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"DeleteConfirmationModal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    // Restore scrolling when modal is closed\n                    document.body.style.overflow = '';\n                }\n            })[\"DeleteConfirmationModal.useEffect\"];\n        }\n    }[\"DeleteConfirmationModal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    if (!isOpen) return null;\n    // Stop propagation to prevent clicks inside the modal from closing it\n    const handleModalClick = (e)=>{\n        e.stopPropagation();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[9999] overflow-y-auto\",\n        role: \"dialog\",\n        \"aria-modal\": \"true\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen p-4 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-75 transition-opacity\",\n                    onClick: onClose,\n                    \"aria-hidden\": \"true\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative inline-block align-bottom bg-[var(--navy)] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    onClick: handleModalClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:flex sm:items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6 text-red-600\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            \"aria-hidden\": \"true\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg leading-6 font-medium text-white\",\n                                                id: \"modal-title\",\n                                                children: \"Delete Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300\",\n                                                    children: [\n                                                        'Are you sure you want to delete \"',\n                                                        itemName,\n                                                        '\"? This action cannot be undone.'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[var(--navy-light)] px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    onClick: onConfirm,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-[var(--navy)] text-base font-medium text-gray-300 hover:bg-[var(--navy-lighter)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/DeleteConfirmationModal.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DeleteConfirmationModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/modals/DeleteConfirmationModal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/modals/EditAnalysisModal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/EditAnalysisModal.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst EditAnalysisModal = ({ isOpen, onClose, onSave, initialName })=>{\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialName);\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (name.trim()) {\n            onSave(name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[200] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-[var(--navy)] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pt-5 pb-4 sm:p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:flex sm:items-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center sm:mt-0 sm:text-left w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg leading-6 font-medium text-white\",\n                                                children: \"Edit Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"analysis-name\",\n                                                        className: \"block text-sm font-medium text-gray-300\",\n                                                        children: \"Analysis Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"analysis-name\",\n                                                        id: \"analysis-name\",\n                                                        className: \"mt-1 block w-full border border-gray-600 bg-[var(--navy-light)] rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-[var(--turquoise)] focus:border-[var(--turquoise)]\",\n                                                        value: name,\n                                                        onChange: (e)=>setName(e.target.value),\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[var(--navy-light)] px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-[var(--turquoise)] text-base font-medium text-white hover:bg-[var(--turquoise-dark)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--turquoise)] sm:ml-3 sm:w-auto sm:text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-[var(--navy)] text-base font-medium text-gray-300 hover:bg-[var(--navy-lighter)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\",\n                                        onClick: onClose,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/components/modals/EditAnalysisModal.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditAnalysisModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/modals/EditAnalysisModal.tsx\n");

/***/ }),

/***/ "(ssr)/./config/environment.ts":
/*!*******************************!*\
  !*** ./config/environment.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   THEME_CONFIG: () => (/* binding */ THEME_CONFIG)\n/* harmony export */ });\n/**\n * Environment configuration for the application\n */ // API configuration\nconst API_CONFIG = {\n    // Base URL for API requests\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',\n    // Whether to use mock data instead of real API calls\n    USE_MOCK_DATA: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || true,\n    // Timeout for API requests in milliseconds\n    TIMEOUT: 10000,\n    // Simulated delay for mock data in milliseconds\n    MOCK_DELAY: 300\n};\n// Application configuration\nconst APP_CONFIG = {\n    // Default currency\n    DEFAULT_CURRENCY: 'usd',\n    // Default time period\n    DEFAULT_TIME_PERIOD: 'quarterly',\n    // Default analysis type\n    DEFAULT_ANALYSIS_TYPE: 'profitability'\n};\n// Theme configuration\nconst THEME_CONFIG = {\n    // Colors for filters\n    FILTER_COLORS: [\n        '#4F46E5',\n        '#10B981',\n        '#F59E0B',\n        '#EF4444',\n        '#8B5CF6'\n    ],\n    // Colors for chart elements\n    CHART_COLORS: {\n        primary: '#4F46E5',\n        secondary: '#10B981',\n        tertiary: '#F59E0B',\n        quaternary: '#EF4444',\n        quinary: '#8B5CF6'\n    },\n    // Colors for financial indicators\n    FINANCIAL_COLORS: {\n        positive: '#10B981',\n        negative: '#EF4444',\n        neutral: '#6B7280'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./config/environment.ts\n");

/***/ }),

/***/ "(ssr)/./context/AppContext.tsx":
/*!********************************!*\
  !*** ./context/AppContext.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAnalysisData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useAnalysisData */ \"(ssr)/./hooks/useAnalysisData.ts\");\n/* harmony import */ var _hooks_useTableData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useTableData */ \"(ssr)/./hooks/useTableData.ts\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config/environment */ \"(ssr)/./config/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ AppProvider,useAppContext auto */ \n\n\n\n\n// Create the context with a default value\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AppProvider = ({ children, initialFilters = [] })=>{\n    // Analysis settings\n    const [selectedAnalysisType, setSelectedAnalysisType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config_environment__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.DEFAULT_ANALYSIS_TYPE);\n    const [selectedTimePeriod, setSelectedTimePeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config_environment__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.DEFAULT_TIME_PERIOD);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear().toString());\n    const [selectedCurrency, setSelectedCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config_environment__WEBPACK_IMPORTED_MODULE_4__.APP_CONFIG.DEFAULT_CURRENCY);\n    // UI state\n    const [analyticsPanelVisible, setAnalyticsPanelVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [rawDataModalVisible, setRawDataModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shareModalVisible, setShareModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize hooks\n    const analysisState = (0,_hooks_useAnalysisData__WEBPACK_IMPORTED_MODULE_2__.useAnalysisData)();\n    const tableState = (0,_hooks_useTableData__WEBPACK_IMPORTED_MODULE_3__.useTableData)({\n        initialFilters\n    });\n    // Actions for settings\n    const setAnalysisType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[setAnalysisType]\": (type)=>{\n            setSelectedAnalysisType(type);\n        }\n    }[\"AppProvider.useCallback[setAnalysisType]\"], []);\n    const setTimePeriod = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[setTimePeriod]\": (period)=>{\n            setSelectedTimePeriod(period);\n        }\n    }[\"AppProvider.useCallback[setTimePeriod]\"], []);\n    const setYear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[setYear]\": (year)=>{\n            setSelectedYear(year);\n        }\n    }[\"AppProvider.useCallback[setYear]\"], []);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[setCurrency]\": (currency)=>{\n            setSelectedCurrency(currency);\n        }\n    }[\"AppProvider.useCallback[setCurrency]\"], []);\n    // Actions for UI\n    const toggleAnalyticsPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[toggleAnalyticsPanel]\": ()=>{\n            setAnalyticsPanelVisible({\n                \"AppProvider.useCallback[toggleAnalyticsPanel]\": (prev)=>!prev\n            }[\"AppProvider.useCallback[toggleAnalyticsPanel]\"]);\n        }\n    }[\"AppProvider.useCallback[toggleAnalyticsPanel]\"], []);\n    const showRawDataModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[showRawDataModal]\": ()=>{\n            setRawDataModalVisible(true);\n        }\n    }[\"AppProvider.useCallback[showRawDataModal]\"], []);\n    const hideRawDataModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[hideRawDataModal]\": ()=>{\n            setRawDataModalVisible(false);\n        }\n    }[\"AppProvider.useCallback[hideRawDataModal]\"], []);\n    const showShareModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[showShareModal]\": ()=>{\n            setShareModalVisible(true);\n        }\n    }[\"AppProvider.useCallback[showShareModal]\"], []);\n    const hideShareModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppProvider.useCallback[hideShareModal]\": ()=>{\n            setShareModalVisible(false);\n        }\n    }[\"AppProvider.useCallback[hideShareModal]\"], []);\n    // Create the context value\n    const contextValue = {\n        // Analysis settings\n        selectedAnalysisType,\n        selectedTimePeriod,\n        selectedYear,\n        selectedCurrency,\n        // UI state\n        analyticsPanelVisible,\n        rawDataModalVisible,\n        shareModalVisible,\n        // Analysis data\n        analysisState,\n        // Table data\n        tableState,\n        // Actions for settings\n        setAnalysisType,\n        setTimePeriod,\n        setYear,\n        setCurrency,\n        // Actions for UI\n        toggleAnalyticsPanel,\n        showRawDataModal,\n        hideRawDataModal,\n        showShareModal,\n        hideShareModal\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/AppContext.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use the app context\nconst useAppContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error('useAppContext must be used within an AppProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./context/AppContext.tsx\n");

/***/ }),

/***/ "(ssr)/./context/PendingActionsContext.tsx":
/*!*******************************************!*\
  !*** ./context/PendingActionsContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PendingActionsProvider: () => (/* binding */ PendingActionsProvider),\n/* harmony export */   usePendingActions: () => (/* binding */ usePendingActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ usePendingActions,PendingActionsProvider auto */ \n\n// This would normally come from an API\nconst mockActionItems = [\n    {\n        id: 1,\n        status: 'Pending'\n    },\n    {\n        id: 2,\n        status: 'Pending'\n    },\n    {\n        id: 3,\n        status: 'Pending'\n    },\n    {\n        id: 4,\n        status: 'Pending'\n    },\n    {\n        id: 5,\n        status: 'Pending'\n    },\n    {\n        id: 6,\n        status: 'Pending'\n    },\n    {\n        id: 7,\n        status: 'Completed'\n    },\n    {\n        id: 8,\n        status: 'Completed'\n    },\n    {\n        id: 9,\n        status: 'Pending'\n    },\n    {\n        id: 10,\n        status: 'Pending'\n    },\n    {\n        id: 11,\n        status: 'Pending'\n    },\n    {\n        id: 12,\n        status: 'Completed'\n    }\n];\nconst PendingActionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    pendingActionsCount: 0\n});\nconst usePendingActions = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PendingActionsContext);\nconst PendingActionsProvider = ({ children })=>{\n    const [pendingActionsCount, setPendingActionsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PendingActionsProvider.useEffect\": ()=>{\n            // In a real app, this would be an API call\n            const fetchPendingActions = {\n                \"PendingActionsProvider.useEffect.fetchPendingActions\": ()=>{\n                    const count = mockActionItems.filter({\n                        \"PendingActionsProvider.useEffect.fetchPendingActions\": (item)=>item.status === 'Pending'\n                    }[\"PendingActionsProvider.useEffect.fetchPendingActions\"]).length;\n                    setPendingActionsCount(count);\n                }\n            }[\"PendingActionsProvider.useEffect.fetchPendingActions\"];\n            fetchPendingActions();\n        // In a real app, you might set up a polling mechanism or websocket here\n        }\n    }[\"PendingActionsProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PendingActionsContext.Provider, {\n        value: {\n            pendingActionsCount\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Developer/Reluvate/oneflow/leviathan/context/PendingActionsContext.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./context/PendingActionsContext.tsx\n");

/***/ }),

/***/ "(ssr)/./data/mockData.ts":
/*!**************************!*\
  !*** ./data/mockData.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analysisViews: () => (/* binding */ analysisViews),\n/* harmony export */   initialDataSources: () => (/* binding */ initialDataSources),\n/* harmony export */   initialTableData: () => (/* binding */ initialTableData),\n/* harmony export */   mockAnalysisData: () => (/* binding */ mockAnalysisData)\n/* harmony export */ });\n// Mock data for analysis views\nconst analysisViews = [\n    {\n        id: 1,\n        name: \"Profitability Analysis\",\n        health: \"green\",\n        children: [\n            {\n                id: 101,\n                name: \"Q1 2024 Profitability\",\n                parentId: 1,\n                health: \"green\",\n                createdAt: \"2025-04-15T09:30:00\",\n                favorite: true\n            },\n            {\n                id: 102,\n                name: \"North America Profitability\",\n                parentId: 1,\n                health: \"amber\",\n                createdAt: \"2025-04-22T14:45:00\"\n            },\n            {\n                id: 103,\n                name: \"Product Line A Analysis\",\n                parentId: 1,\n                health: \"green\",\n                createdAt: \"2025-05-01T11:20:00\",\n                favorite: true\n            }\n        ]\n    },\n    {\n        id: 2,\n        name: \"Cash Flow Analysis\",\n        health: \"green\",\n        children: [\n            {\n                id: 201,\n                name: \"Q1 2024 Cash Flow\",\n                parentId: 2,\n                health: \"green\",\n                createdAt: \"2025-04-10T16:15:00\",\n                favorite: true\n            },\n            {\n                id: 202,\n                name: \"Working Capital Analysis\",\n                parentId: 2,\n                health: \"amber\",\n                createdAt: \"2025-04-28T10:05:00\"\n            },\n            {\n                id: 203,\n                name: \"Cash Conversion Cycle\",\n                parentId: 2,\n                health: \"green\",\n                createdAt: \"2025-05-05T13:45:00\"\n            }\n        ]\n    },\n    {\n        id: 3,\n        name: \"Revenue Analysis\",\n        health: \"amber\",\n        children: [\n            {\n                id: 301,\n                name: \"Revenue by Region\",\n                parentId: 3,\n                health: \"green\",\n                createdAt: \"2025-04-05T13:40:00\"\n            },\n            {\n                id: 302,\n                name: \"Revenue by Product\",\n                parentId: 3,\n                health: \"amber\",\n                createdAt: \"2025-05-07T09:15:00\"\n            }\n        ]\n    },\n    {\n        id: 4,\n        name: \"Expense Analysis\",\n        health: \"red\"\n    },\n    {\n        id: 5,\n        name: \"Balance Sheet Health\",\n        health: \"green\"\n    },\n    {\n        id: 6,\n        name: \"Working Capital Management\",\n        health: \"amber\"\n    },\n    {\n        id: 7,\n        name: \"Investment Analysis\",\n        health: \"green\"\n    }\n];\n// Mock data for the narrative analysis\nconst mockAnalysisData = {\n    title: \"Profitability Analysis\",\n    summary: \"Comprehensive view of company profitability metrics across different dimensions.\",\n    sections: [\n        {\n            id: \"overview\",\n            title: \"Executive Summary\",\n            content: \"<p class='text-black'>Fiscal year 2024 key profitability highlights:</p>\\n\\n<ol class='list-decimal pl-5 space-y-2 text-black'>\\n  <li>Gross profit margins increased by <span class='font-bold text-black border-b-4 border-green-600'>+2.3%</span> year-over-year</li>\\n  <li>Primary drivers:\\n    <ul class='list-disc pl-5 mt-1 space-y-1'>\\n      <li>Operational efficiencies (unit production costs <span class='font-bold text-black border-b-4 border-green-600'>-5.2%</span>)</li>\\n      <li>Strategic pricing adjustments (average selling price <span class='font-bold text-black border-b-4 border-green-600'>+3.1%</span>)</li>\\n    </ul>\\n  </li>\\n  <li>EBITDA trending <span class='font-bold text-black border-b-4 border-green-600'>+5%</span> above industry average</li>\\n  <li>Product line A remains strongest performer with <span class='font-bold text-black border-b-4 border-green-600'>42%</span> contribution margin</li>\\n</ol>\"\n        },\n        {\n            id: \"margin-analysis\",\n            title: \"Margin Analysis\",\n            content: \"<p class='text-black'>Our gross margin improvement can be attributed to three key factors:</p>\\n\\n<ol class='list-decimal pl-5 space-y-2 text-black'>\\n  <li>Reduced raw material costs <span class='font-bold text-black border-b-4 border-red-600'>-12%</span> through strategic sourcing initiatives</li>\\n  <li>Increased production efficiency <span class='font-bold text-black border-b-4 border-green-600'>+15%</span> from our plant modernization program</li>\\n  <li>Favorable product mix shift toward higher-margin offerings</li>\\n</ol>\\n\\n<p class='text-black mt-3'>The chart below illustrates the quarterly progression of our margin improvement.</p>\"\n        },\n        {\n            id: \"product-performance\",\n            title: \"Product Line Performance\",\n            content: \"<p class='text-black'>Product line performance summary:</p>\\n\\n<ol class='list-decimal pl-5 space-y-2 text-black'>\\n  <li>Product line A: Maintained position as highest-margin offering with contribution margins reaching <span class='font-bold text-black border-b-4 border-green-600'>42%</span> <span class='font-bold text-black border-b-4 border-green-600'>+3.5pp</span> over previous year</li>\\n  <li>Product line B: Showed modest improvement of <span class='font-bold text-black border-b-4 border-green-600'>+1.2pp</span></li>\\n  <li>Product line C: Smallest improvement at <span class='font-bold text-black border-b-4 border-green-600'>+0.8pp</span></li>\\n</ol>\\n\\n<p class='text-black mt-3'>The relative performance of each product line is visualized below.</p>\"\n        },\n        {\n            id: \"ebitda-analysis\",\n            title: \"EBITDA Analysis\",\n            content: \"<p class='text-black'>EBITDA performance highlights:</p>\\n\\n<ol class='list-decimal pl-5 space-y-2 text-black'>\\n  <li>EBITDA growth to <span class='font-bold text-black border-b-4 border-green-600'>$24.3M</span> <span class='font-bold text-black border-b-4 border-green-600'>+15%</span> year-over-year</li>\\n  <li>Performance <span class='font-bold text-black border-b-4 border-green-600'>+5%</span> above industry average</li>\\n  <li>Improvement drivers:\\n    <ul class='list-disc pl-5 mt-1 space-y-1'>\\n      <li>Gross margin improvements as detailed above</li>\\n      <li>Disciplined SG&A management (SG&A ratio reduced from <span class='font-bold text-black border-b-4 border-red-600'>22.1%</span> to <span class='font-bold text-black border-b-4 border-green-600'>20.3%</span>)</li>\\n    </ul>\\n  </li>\\n  <li>EBITDA margin now at <span class='font-bold text-black border-b-4 border-green-600'>18.7%</span> (up from <span class='font-bold text-black border-b-4 border-red-600'>16.2%</span> in previous year)</li>\\n</ol>\"\n        },\n        {\n            id: \"outlook\",\n            title: \"Outlook & Recommendations\",\n            content: \"<p class='text-black'>Based on current trends and forward indicators, we project continued margin improvement in the coming fiscal year. To maximize profitability, we recommend:</p>\\n\\n<ol class='list-decimal pl-5 space-y-2 text-black'>\\n  <li>Expand production capacity for Product line A (projected ROI: <span class='font-bold text-black border-b-4 border-green-600'>32%</span>)</li>\\n  <li>Implement cost reduction program for Product line C (estimated savings of <span class='font-bold text-black border-b-4 border-green-600'>$1.2M</span>)</li>\\n  <li>Evaluate price adjustments for Product line B to improve its contribution margin</li>\\n  <li>Address rising logistics costs which increased by <span class='font-bold text-black border-b-4 border-red-600'>+8%</span> in Q4</li>\\n</ol>\"\n        }\n    ],\n    data: {\n        quarterlyResults: [\n            {\n                period: \"Q1 2024\",\n                revenue: 5840000,\n                cogs: 3212000,\n                grossProfit: 2628000,\n                margin: 45.0\n            },\n            {\n                period: \"Q2 2024\",\n                revenue: 6120000,\n                cogs: 3304800,\n                grossProfit: 2815200,\n                margin: 46.0\n            },\n            {\n                period: \"Q3 2024\",\n                revenue: 6750000,\n                cogs: 3577500,\n                grossProfit: 3172500,\n                margin: 47.0\n            },\n            {\n                period: \"Q4 2024\",\n                revenue: 7320000,\n                cogs: 3806400,\n                grossProfit: 3513600,\n                margin: 48.0\n            }\n        ]\n    },\n    currentVersion: {\n        id: \"v1\",\n        isBase: true,\n        createdAt: \"2025-05-01T10:30:00\"\n    },\n    versions: [\n        {\n            id: \"v1\",\n            isBase: true,\n            createdAt: \"2025-05-01T10:30:00\"\n        },\n        {\n            id: \"v2\",\n            isBase: false,\n            createdAt: \"2025-05-05T14:45:00\"\n        },\n        {\n            id: \"v3\",\n            isBase: false,\n            createdAt: \"2025-05-08T09:15:00\"\n        }\n    ]\n};\n// Initial data sources\nconst initialDataSources = [\n    {\n        id: 1,\n        name: \"Financial Database\",\n        color: \"bg-blue-100 text-blue-800\"\n    },\n    {\n        id: 2,\n        name: \"CRM Data\",\n        color: \"bg-green-100 text-green-800\"\n    },\n    {\n        id: 3,\n        name: \"ERP System\",\n        color: \"bg-orange-100 text-orange-800\"\n    }\n];\n// Initial table data with extended information\nconst initialTableData = [\n    // First set of data\n    {\n        period: \"Q1 2024\",\n        revenue: 5840000,\n        cogs: 3212000,\n        grossProfit: 2628000,\n        margin: 45.0,\n        source: \"Financial Database\",\n        unit: \"All\"\n    },\n    {\n        period: \"Q2 2024\",\n        revenue: 6120000,\n        cogs: 3304800,\n        grossProfit: 2815200,\n        margin: 46.0,\n        source: \"Financial Database\",\n        unit: \"All\"\n    },\n    {\n        period: \"Q3 2024\",\n        revenue: 6750000,\n        cogs: 3577500,\n        grossProfit: 3172500,\n        margin: 47.0,\n        source: \"Financial Database\",\n        unit: \"All\"\n    },\n    {\n        period: \"Q4 2024\",\n        revenue: 7320000,\n        cogs: 3806400,\n        grossProfit: 3513600,\n        margin: 48.0,\n        source: \"Financial Database\",\n        unit: \"All\"\n    },\n    // Second set of data\n    {\n        period: \"Q1 2024\",\n        revenue: 2840000,\n        cogs: 1612000,\n        grossProfit: 1228000,\n        margin: 43.2,\n        source: \"CRM Data\",\n        unit: \"North America\"\n    },\n    {\n        period: \"Q2 2024\",\n        revenue: 3120000,\n        cogs: 1704800,\n        grossProfit: 1415200,\n        margin: 45.4,\n        source: \"CRM Data\",\n        unit: \"North America\"\n    },\n    {\n        period: \"Q3 2024\",\n        revenue: 3750000,\n        cogs: 1977500,\n        grossProfit: 1772500,\n        margin: 47.3,\n        source: \"CRM Data\",\n        unit: \"North America\"\n    },\n    {\n        period: \"Q4 2024\",\n        revenue: 4320000,\n        cogs: 2306400,\n        grossProfit: 2013600,\n        margin: 46.6,\n        source: \"CRM Data\",\n        unit: \"North America\"\n    },\n    // Third set of data\n    {\n        period: \"Q1 2024\",\n        revenue: 1840000,\n        cogs: 1012000,\n        grossProfit: 828000,\n        margin: 45.0,\n        source: \"ERP System\",\n        unit: \"Europe\"\n    },\n    {\n        period: \"Q2 2024\",\n        revenue: 2120000,\n        cogs: 1104800,\n        grossProfit: 1015200,\n        margin: 47.9,\n        source: \"ERP System\",\n        unit: \"Europe\"\n    },\n    {\n        period: \"Q3 2024\",\n        revenue: 2750000,\n        cogs: 1477500,\n        grossProfit: 1272500,\n        margin: 46.3,\n        source: \"ERP System\",\n        unit: \"Europe\"\n    },\n    {\n        period: \"Q4 2024\",\n        revenue: 3320000,\n        cogs: 1806400,\n        grossProfit: 1513600,\n        margin: 45.6,\n        source: \"ERP System\",\n        unit: \"Europe\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./data/mockData.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useAnalysisData.ts":
/*!**********************************!*\
  !*** ./hooks/useAnalysisData.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAnalysisData: () => (/* binding */ useAnalysisData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api */ \"(ssr)/./services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAnalysisData auto */ \n\n/**\n * Custom hook for managing analysis data\n */ const useAnalysisData = ()=>{\n    // State for analysis views and data\n    const [analysisViews, setAnalysisViews] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [activeViewId, setActiveViewId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [analysisData, setAnalysisData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch analysis views\n    const fetchViews = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[fetchViews]\": async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                const views = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.fetchAnalysisViews)();\n                setAnalysisViews(views);\n                if (views.length > 0 && !activeViewId) {\n                    setActiveViewId(views[0].id);\n                }\n                return views;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error('Failed to fetch analysis views'));\n                return [];\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useAnalysisData.useCallback[fetchViews]\"], [\n        activeViewId\n    ]);\n    // Fetch data for the active view\n    const fetchViewData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[fetchViewData]\": async (viewId)=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                const data = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.fetchAnalysisData)(viewId);\n                setAnalysisData(data);\n                return data;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(`Failed to fetch data for view ${viewId}`));\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useAnalysisData.useCallback[fetchViewData]\"], []);\n    // Set the active view and fetch its data\n    const setActiveView = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[setActiveView]\": async (viewId)=>{\n            setActiveViewId(viewId);\n            return fetchViewData(viewId);\n        }\n    }[\"useAnalysisData.useCallback[setActiveView]\"], [\n        fetchViewData\n    ]);\n    // Toggle favorite status for a view\n    const handleToggleFavorite = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[handleToggleFavorite]\": async (viewId)=>{\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.toggleFavorite)(viewId);\n                if (result.success) {\n                    // Update the views list to reflect the change\n                    setAnalysisViews({\n                        \"useAnalysisData.useCallback[handleToggleFavorite]\": (prevViews)=>{\n                            return prevViews.map({\n                                \"useAnalysisData.useCallback[handleToggleFavorite]\": (view)=>{\n                                    if (view.id === viewId) {\n                                        return {\n                                            ...view,\n                                            favorite: result.favorite\n                                        };\n                                    }\n                                    // Check children if this is a parent view\n                                    if (view.children) {\n                                        const updatedChildren = view.children.map({\n                                            \"useAnalysisData.useCallback[handleToggleFavorite].updatedChildren\": (child)=>{\n                                                if (child.id === viewId) {\n                                                    return {\n                                                        ...child,\n                                                        favorite: result.favorite\n                                                    };\n                                                }\n                                                return child;\n                                            }\n                                        }[\"useAnalysisData.useCallback[handleToggleFavorite].updatedChildren\"]);\n                                        return {\n                                            ...view,\n                                            children: updatedChildren\n                                        };\n                                    }\n                                    return view;\n                                }\n                            }[\"useAnalysisData.useCallback[handleToggleFavorite]\"]);\n                        }\n                    }[\"useAnalysisData.useCallback[handleToggleFavorite]\"]);\n                }\n                return result;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(`Failed to toggle favorite for view ${viewId}`));\n                return {\n                    id: viewId,\n                    favorite: false,\n                    success: false\n                };\n            }\n        }\n    }[\"useAnalysisData.useCallback[handleToggleFavorite]\"], []);\n    // Edit a view's details\n    const handleEditView = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[handleEditView]\": async (viewId, data)=>{\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.editAnalysisView)(viewId, data);\n                if (result.success) {\n                    // Update the views list to reflect the change\n                    setAnalysisViews({\n                        \"useAnalysisData.useCallback[handleEditView]\": (prevViews)=>{\n                            return prevViews.map({\n                                \"useAnalysisData.useCallback[handleEditView]\": (view)=>{\n                                    if (view.id === viewId) {\n                                        return {\n                                            ...view,\n                                            ...data\n                                        };\n                                    }\n                                    // Check children if this is a parent view\n                                    if (view.children) {\n                                        const updatedChildren = view.children.map({\n                                            \"useAnalysisData.useCallback[handleEditView].updatedChildren\": (child)=>{\n                                                if (child.id === viewId) {\n                                                    return {\n                                                        ...child,\n                                                        ...data\n                                                    };\n                                                }\n                                                return child;\n                                            }\n                                        }[\"useAnalysisData.useCallback[handleEditView].updatedChildren\"]);\n                                        return {\n                                            ...view,\n                                            children: updatedChildren\n                                        };\n                                    }\n                                    return view;\n                                }\n                            }[\"useAnalysisData.useCallback[handleEditView]\"]);\n                        }\n                    }[\"useAnalysisData.useCallback[handleEditView]\"]);\n                    // Update analysis data if this is the active view\n                    if (activeViewId === viewId && analysisData && data.name) {\n                        setAnalysisData({\n                            ...analysisData,\n                            title: data.name\n                        });\n                    }\n                }\n                return result;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(`Failed to edit view ${viewId}`));\n                return {\n                    id: viewId,\n                    success: false\n                };\n            }\n        }\n    }[\"useAnalysisData.useCallback[handleEditView]\"], [\n        activeViewId,\n        analysisData\n    ]);\n    // Delete a view\n    const handleDeleteView = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[handleDeleteView]\": async (viewId)=>{\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.deleteAnalysisView)(viewId);\n                if (result.success) {\n                    // Remove the view from the list\n                    setAnalysisViews({\n                        \"useAnalysisData.useCallback[handleDeleteView]\": (prevViews)=>{\n                            // Check if it's a top-level view\n                            const filteredViews = prevViews.filter({\n                                \"useAnalysisData.useCallback[handleDeleteView].filteredViews\": (view)=>view.id !== viewId\n                            }[\"useAnalysisData.useCallback[handleDeleteView].filteredViews\"]);\n                            // If the list is the same length, it might be a child view\n                            if (filteredViews.length === prevViews.length) {\n                                return prevViews.map({\n                                    \"useAnalysisData.useCallback[handleDeleteView]\": (view)=>{\n                                        if (view.children) {\n                                            return {\n                                                ...view,\n                                                children: view.children.filter({\n                                                    \"useAnalysisData.useCallback[handleDeleteView]\": (child)=>child.id !== viewId\n                                                }[\"useAnalysisData.useCallback[handleDeleteView]\"])\n                                            };\n                                        }\n                                        return view;\n                                    }\n                                }[\"useAnalysisData.useCallback[handleDeleteView]\"]);\n                            }\n                            return filteredViews;\n                        }\n                    }[\"useAnalysisData.useCallback[handleDeleteView]\"]);\n                    // If the deleted view was active, select another view\n                    if (activeViewId === viewId) {\n                        const remainingViews = analysisViews.filter({\n                            \"useAnalysisData.useCallback[handleDeleteView].remainingViews\": (view)=>view.id !== viewId\n                        }[\"useAnalysisData.useCallback[handleDeleteView].remainingViews\"]);\n                        if (remainingViews.length > 0) {\n                            setActiveViewId(remainingViews[0].id);\n                            fetchViewData(remainingViews[0].id);\n                        } else {\n                            setActiveViewId(null);\n                            setAnalysisData(null);\n                        }\n                    }\n                }\n                return result;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(`Failed to delete view ${viewId}`));\n                return {\n                    success: false,\n                    message: 'Failed to delete analysis view'\n                };\n            }\n        }\n    }[\"useAnalysisData.useCallback[handleDeleteView]\"], [\n        activeViewId,\n        analysisViews,\n        fetchViewData\n    ]);\n    // Create a new analysis\n    const handleCreateAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[handleCreateAnalysis]\": async (parentId, name, filters, measures)=>{\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.saveAnalysis)(parentId, name, filters, measures);\n                // Update the views list to include the new analysis\n                setAnalysisViews({\n                    \"useAnalysisData.useCallback[handleCreateAnalysis]\": (prevViews)=>{\n                        return prevViews.map({\n                            \"useAnalysisData.useCallback[handleCreateAnalysis]\": (view)=>{\n                                if (view.id === parentId) {\n                                    const children = view.children || [];\n                                    return {\n                                        ...view,\n                                        children: [\n                                            ...children,\n                                            result\n                                        ]\n                                    };\n                                }\n                                return view;\n                            }\n                        }[\"useAnalysisData.useCallback[handleCreateAnalysis]\"]);\n                    }\n                }[\"useAnalysisData.useCallback[handleCreateAnalysis]\"]);\n                return result;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error('Failed to create analysis'));\n                throw err;\n            }\n        }\n    }[\"useAnalysisData.useCallback[handleCreateAnalysis]\"], []);\n    // Update an existing analysis\n    const handleUpdateAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAnalysisData.useCallback[handleUpdateAnalysis]\": async (viewId, filters, measures)=>{\n            try {\n                const result = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.updateAnalysis)(viewId, filters, measures);\n                // Update the analysis data if this is the active view\n                if (activeViewId === viewId) {\n                    setAnalysisData(result);\n                }\n                return result;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error(`Failed to update analysis ${viewId}`));\n                throw err;\n            }\n        }\n    }[\"useAnalysisData.useCallback[handleUpdateAnalysis]\"], [\n        activeViewId\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAnalysisData.useEffect\": ()=>{\n            const loadInitialData = {\n                \"useAnalysisData.useEffect.loadInitialData\": async ()=>{\n                    const views = await fetchViews();\n                    if (views.length > 0) {\n                        const firstViewId = views[0].id;\n                        setActiveViewId(firstViewId);\n                        await fetchViewData(firstViewId);\n                    }\n                }\n            }[\"useAnalysisData.useEffect.loadInitialData\"];\n            loadInitialData();\n        }\n    }[\"useAnalysisData.useEffect\"], [\n        fetchViews,\n        fetchViewData\n    ]);\n    // Fetch data when active view changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAnalysisData.useEffect\": ()=>{\n            if (activeViewId) {\n                fetchViewData(activeViewId);\n            }\n        }\n    }[\"useAnalysisData.useEffect\"], [\n        activeViewId,\n        fetchViewData\n    ]);\n    return {\n        analysisViews,\n        activeViewId,\n        analysisData,\n        isLoading,\n        error,\n        setActiveView,\n        toggleFavorite: handleToggleFavorite,\n        editView: handleEditView,\n        deleteView: handleDeleteView,\n        createAnalysis: handleCreateAnalysis,\n        updateAnalysis: handleUpdateAnalysis,\n        refreshViews: fetchViews\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useAnalysisData.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useTableData.ts":
/*!*******************************!*\
  !*** ./hooks/useTableData.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTableData: () => (/* binding */ useTableData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api */ \"(ssr)/./services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useTableData auto */ \n\n/**\n * Custom hook for managing table data with pagination\n */ const useTableData = ({ pageSize = 20, initialFilters = [] } = {})=>{\n    const [tableData, setTableData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [filteredData, setFilteredData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [displayData, setDisplayData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilters);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch table data\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[loadData]\": async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                const data = await (0,_services_api__WEBPACK_IMPORTED_MODULE_1__.fetchTableData)();\n                setTableData(data);\n                return data;\n            } catch (err) {\n                setError(err instanceof Error ? err : new Error('Failed to fetch table data'));\n                return [];\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useTableData.useCallback[loadData]\"], []);\n    // Apply filters to data\n    const applyFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[applyFilters]\": ()=>{\n            if (filters.length === 0) {\n                setFilteredData(tableData);\n                return;\n            }\n            // Apply filters to the data\n            // This is a simplified example - in a real app, you'd have more sophisticated filtering\n            const filtered = tableData.filter({\n                \"useTableData.useCallback[applyFilters].filtered\": (row)=>{\n                    // Check if row matches all filters\n                    return filters.every({\n                        \"useTableData.useCallback[applyFilters].filtered\": (filter)=>{\n                            // Simple string matching for demonstration\n                            const filterValue = filter.value.toLowerCase();\n                            // Check various fields for matches - dynamically check properties\n                            // This handles the case where the ExtendedRowData type might not have these properties defined\n                            return Object.keys(row).some({\n                                \"useTableData.useCallback[applyFilters].filtered\": (key)=>{\n                                    const value = row[key];\n                                    return typeof value === 'string' && value.toLowerCase().includes(filterValue);\n                                }\n                            }[\"useTableData.useCallback[applyFilters].filtered\"]);\n                        }\n                    }[\"useTableData.useCallback[applyFilters].filtered\"]);\n                }\n            }[\"useTableData.useCallback[applyFilters].filtered\"]);\n            setFilteredData(filtered);\n        }\n    }[\"useTableData.useCallback[applyFilters]\"], [\n        tableData,\n        filters\n    ]);\n    // Update pagination\n    const updatePagination = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[updatePagination]\": ()=>{\n            const total = Math.ceil(filteredData.length / pageSize);\n            setTotalPages(Math.max(1, total));\n            // Adjust current page if it's out of bounds\n            if (currentPage > total) {\n                setCurrentPage(Math.max(1, total));\n            }\n            // Get data for the current page\n            const start = (currentPage - 1) * pageSize;\n            const end = start + pageSize;\n            setDisplayData(filteredData.slice(start, end));\n        }\n    }[\"useTableData.useCallback[updatePagination]\"], [\n        filteredData,\n        currentPage,\n        pageSize\n    ]);\n    // Add a filter\n    const addFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[addFilter]\": (filter)=>{\n            setFilters({\n                \"useTableData.useCallback[addFilter]\": (prev)=>[\n                        ...prev,\n                        filter\n                    ]\n            }[\"useTableData.useCallback[addFilter]\"]);\n        }\n    }[\"useTableData.useCallback[addFilter]\"], []);\n    // Remove a filter\n    const removeFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[removeFilter]\": (filterId)=>{\n            setFilters({\n                \"useTableData.useCallback[removeFilter]\": (prev)=>prev.filter({\n                        \"useTableData.useCallback[removeFilter]\": (f)=>f.id !== filterId\n                    }[\"useTableData.useCallback[removeFilter]\"])\n            }[\"useTableData.useCallback[removeFilter]\"]);\n        }\n    }[\"useTableData.useCallback[removeFilter]\"], []);\n    // Clear all filters\n    const clearFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[clearFilters]\": ()=>{\n            setFilters([]);\n        }\n    }[\"useTableData.useCallback[clearFilters]\"], []);\n    // Go to a specific page\n    const goToPage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[goToPage]\": (page)=>{\n            const targetPage = Math.max(1, Math.min(page, totalPages));\n            setCurrentPage(targetPage);\n        }\n    }[\"useTableData.useCallback[goToPage]\"], [\n        totalPages\n    ]);\n    // Go to the next page\n    const nextPage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[nextPage]\": ()=>{\n            if (currentPage < totalPages) {\n                setCurrentPage({\n                    \"useTableData.useCallback[nextPage]\": (prev)=>prev + 1\n                }[\"useTableData.useCallback[nextPage]\"]);\n            }\n        }\n    }[\"useTableData.useCallback[nextPage]\"], [\n        currentPage,\n        totalPages\n    ]);\n    // Go to the previous page\n    const prevPage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useTableData.useCallback[prevPage]\": ()=>{\n            if (currentPage > 1) {\n                setCurrentPage({\n                    \"useTableData.useCallback[prevPage]\": (prev)=>prev - 1\n                }[\"useTableData.useCallback[prevPage]\"]);\n            }\n        }\n    }[\"useTableData.useCallback[prevPage]\"], [\n        currentPage\n    ]);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTableData.useEffect\": ()=>{\n            loadData();\n        }\n    }[\"useTableData.useEffect\"], [\n        loadData\n    ]);\n    // Apply filters when data or filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTableData.useEffect\": ()=>{\n            applyFilters();\n        }\n    }[\"useTableData.useEffect\"], [\n        tableData,\n        filters,\n        applyFilters\n    ]);\n    // Update pagination when filtered data or current page changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTableData.useEffect\": ()=>{\n            updatePagination();\n        }\n    }[\"useTableData.useEffect\"], [\n        filteredData,\n        currentPage,\n        updatePagination\n    ]);\n    return {\n        // Data\n        allData: tableData,\n        filteredData,\n        displayData,\n        // Pagination\n        currentPage,\n        totalPages,\n        goToPage,\n        nextPage,\n        prevPage,\n        // Filters\n        filters,\n        addFilter,\n        removeFilter,\n        clearFilters,\n        // Status\n        isLoading,\n        error,\n        // Actions\n        refreshData: loadData\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useTableData.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/team/page.tsx */ \"(ssr)/./app/team/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGc2hlbmclMkZEZXZlbG9wZXIlMkZSZWx1dmF0ZSUyRm9uZWZsb3clMkZsZXZpYXRoYW4lMkZhcHAlMkZ0ZWFtJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUF3RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoZW5nL0RldmVsb3Blci9SZWx1dmF0ZS9vbmVmbG93L2xldmlhdGhhbi9hcHAvdGVhbS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fteam%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./context/AppContext.tsx */ \"(ssr)/./context/AppContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./context/PendingActionsContext.tsx */ \"(ssr)/./context/PendingActionsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fcontext%2FPendingActionsContext.tsx%22%2C%22ids%22%3A%5B%22PendingActionsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteAnalysisView: () => (/* binding */ deleteAnalysisView),\n/* harmony export */   editAnalysisView: () => (/* binding */ editAnalysisView),\n/* harmony export */   fetchAnalysisData: () => (/* binding */ fetchAnalysisData),\n/* harmony export */   fetchAnalysisViews: () => (/* binding */ fetchAnalysisViews),\n/* harmony export */   fetchDataSources: () => (/* binding */ fetchDataSources),\n/* harmony export */   fetchTableData: () => (/* binding */ fetchTableData),\n/* harmony export */   saveAnalysis: () => (/* binding */ saveAnalysis),\n/* harmony export */   toggleFavorite: () => (/* binding */ toggleFavorite),\n/* harmony export */   updateAnalysis: () => (/* binding */ updateAnalysis)\n/* harmony export */ });\n/* harmony import */ var _apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./apiClient */ \"(ssr)/./services/apiClient.ts\");\n/* harmony import */ var _mockService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mockService */ \"(ssr)/./services/mockService.ts\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/environment */ \"(ssr)/./config/environment.ts\");\n\n\n\n/**\n * API service for handling data operations\n * Uses either real API calls or mock data based on configuration\n */ /**\n * Fetch analysis views\n */ const fetchAnalysisViews = async ()=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getAnalysisViews();\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/analysis-views/');\n    } catch (error) {\n        console.error('Error fetching analysis views:', error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getAnalysisViews();\n    }\n};\n/**\n * Fetch analysis data for a specific view\n */ const fetchAnalysisData = async (viewId)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getAnalysisData(viewId);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(`/analysis-data/${viewId}/`);\n    } catch (error) {\n        console.error(`Error fetching analysis data for view ${viewId}:`, error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getAnalysisData(viewId);\n    }\n};\n/**\n * Fetch table data\n */ const fetchTableData = async ()=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getTableData();\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/table-data/');\n    } catch (error) {\n        console.error('Error fetching table data:', error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getTableData();\n    }\n};\n/**\n * Fetch data sources\n */ const fetchDataSources = async ()=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getDataSources();\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/data-sources/');\n    } catch (error) {\n        console.error('Error fetching data sources:', error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.getDataSources();\n    }\n};\n/**\n * Save a new analysis as a sub-item of a parent analysis\n */ const saveAnalysis = async (parentId, name, filters, measures)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.saveAnalysis(parentId, name, filters, measures);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/analysis-views/', {\n            parentId,\n            name,\n            filters,\n            measures\n        });\n    } catch (error) {\n        console.error('Error saving analysis:', error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.saveAnalysis(parentId, name, filters, measures);\n    }\n};\n/**\n * Update an existing analysis with new filters/measures\n */ const updateAnalysis = async (viewId, filters, measures)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.updateAnalysis(viewId, filters, measures);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(`/analysis-data/${viewId}/`, {\n            filters,\n            measures\n        });\n    } catch (error) {\n        console.error(`Error updating analysis ${viewId}:`, error);\n        // Fallback to mock data in case of error\n        return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.updateAnalysis(viewId, filters, measures);\n    }\n};\n/**\n * Toggle favorite status for an analysis view\n */ const toggleFavorite = async (viewId)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.toggleFavorite(viewId);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(`/analysis-views/${viewId}/favorite/`);\n    } catch (error) {\n        console.error(`Error toggling favorite for analysis ${viewId}:`, error);\n        // Return a failure response\n        return {\n            id: viewId,\n            favorite: false,\n            success: false\n        };\n    }\n};\n/**\n * Edit an analysis view\n */ const editAnalysisView = async (viewId, data)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.editAnalysisView(viewId, data);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(`/analysis-views/${viewId}/`, data);\n    } catch (error) {\n        console.error(`Error editing analysis ${viewId}:`, error);\n        // Return a failure response\n        return {\n            id: viewId,\n            success: false\n        };\n    }\n};\n/**\n * Delete an analysis view\n */ const deleteAnalysisView = async (viewId)=>{\n    try {\n        if (_config_environment__WEBPACK_IMPORTED_MODULE_2__.API_CONFIG.USE_MOCK_DATA) {\n            return await _mockService__WEBPACK_IMPORTED_MODULE_1__.mockService.deleteAnalysisView(viewId);\n        }\n        return await _apiClient__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(`/analysis-views/${viewId}/delete/`);\n    } catch (error) {\n        console.error(`Error deleting analysis ${viewId}:`, error);\n        // Return a failure response\n        return {\n            success: false,\n            message: 'Failed to delete analysis view'\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/api.ts\n");

/***/ }),

/***/ "(ssr)/./services/apiClient.ts":
/*!*******************************!*\
  !*** ./services/apiClient.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/environment */ \"(ssr)/./config/environment.ts\");\n\n\n/**\n * Base API client for making HTTP requests\n */ class ApiClient {\n    constructor(baseURL){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL,\n            timeout: _config_environment__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.TIMEOUT,\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        // Add request interceptor for auth tokens, etc. if needed\n        this.client.interceptors.request.use((config)=>{\n            // Add auth token if available\n            // const token = getAuthToken();\n            // if (token) {\n            //   config.headers.Authorization = `Bearer ${token}`;\n            // }\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Add response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            // Handle specific error cases\n            if (error.response) {\n                // Server responded with an error status\n                console.error('API Error:', error.response.status, error.response.data);\n                // Handle specific status codes\n                switch(error.response.status){\n                    case 401:\n                        break;\n                    case 404:\n                        break;\n                    default:\n                        break;\n                }\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error('API Error: No response received', error.request);\n            } else {\n                // Error in setting up the request\n                console.error('API Error:', error.message);\n            }\n            return Promise.reject(error);\n        });\n    }\n    /**\n   * Make a GET request\n   */ async get(url, config) {\n        const response = await this.client.get(url, config);\n        return response.data;\n    }\n    /**\n   * Make a POST request\n   */ async post(url, data, config) {\n        const response = await this.client.post(url, data, config);\n        return response.data;\n    }\n    /**\n   * Make a PUT request\n   */ async put(url, data, config) {\n        const response = await this.client.put(url, data, config);\n        return response.data;\n    }\n    /**\n   * Make a DELETE request\n   */ async delete(url, config) {\n        const response = await this.client.delete(url, config);\n        return response.data;\n    }\n}\n// Create and export a singleton instance\nconst apiClient = new ApiClient(_config_environment__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.BASE_URL);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./services/mockService.ts":
/*!*********************************!*\
  !*** ./services/mockService.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockService: () => (/* binding */ mockService)\n/* harmony export */ });\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/environment */ \"(ssr)/./config/environment.ts\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/mockData */ \"(ssr)/./data/mockData.ts\");\n\n\n/**\n * Helper function to simulate API delay\n */ const simulateApiDelay = (data)=>{\n    return new Promise((resolve)=>setTimeout(()=>resolve(data), _config_environment__WEBPACK_IMPORTED_MODULE_0__.API_CONFIG.MOCK_DELAY));\n};\n/**\n * Mock service for handling mock data\n */ class MockService {\n    /**\n   * Get mock analysis views\n   */ async getAnalysisViews() {\n        return simulateApiDelay(_data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews);\n    }\n    /**\n   * Get mock analysis data for a specific view\n   */ async getAnalysisData(viewId) {\n        // In a more sophisticated mock service, we could filter by viewId\n        return simulateApiDelay(_data_mockData__WEBPACK_IMPORTED_MODULE_1__.mockAnalysisData);\n    }\n    /**\n   * Get mock table data\n   */ async getTableData() {\n        return simulateApiDelay(_data_mockData__WEBPACK_IMPORTED_MODULE_1__.initialTableData);\n    }\n    /**\n   * Get mock data sources\n   */ async getDataSources() {\n        return simulateApiDelay(_data_mockData__WEBPACK_IMPORTED_MODULE_1__.initialDataSources);\n    }\n    /**\n   * Save a new analysis (mock)\n   */ async saveAnalysis(parentId, name, filters, measures) {\n        const savedAnalysis = {\n            id: Math.floor(Math.random() * 1000) + 100,\n            name: name,\n            parentId: parentId,\n            health: 'green',\n            children: []\n        };\n        return simulateApiDelay(savedAnalysis);\n    }\n    /**\n   * Update an existing analysis (mock)\n   */ async updateAnalysis(viewId, filters, measures) {\n        return simulateApiDelay(_data_mockData__WEBPACK_IMPORTED_MODULE_1__.mockAnalysisData);\n    }\n    /**\n   * Toggle favorite status for an analysis view (mock)\n   */ async toggleFavorite(viewId) {\n        // Find the view in our mock data to toggle its favorite status\n        let targetView;\n        // Search through all parent views and their children\n        for (const view of _data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews){\n            if (view.id === viewId) {\n                targetView = view;\n                break;\n            }\n            if (view.children) {\n                const childView = view.children.find((child)=>child.id === viewId);\n                if (childView) {\n                    targetView = childView;\n                    break;\n                }\n            }\n        }\n        if (targetView) {\n            targetView.favorite = !targetView.favorite;\n            const result = {\n                id: targetView.id,\n                favorite: targetView.favorite,\n                success: true\n            };\n            return simulateApiDelay(result);\n        }\n        return simulateApiDelay({\n            id: viewId,\n            favorite: false,\n            success: false\n        });\n    }\n    /**\n   * Edit an analysis view (mock)\n   */ async editAnalysisView(viewId, data) {\n        // Find the view in our mock data to update it\n        let targetView;\n        // Search through all parent views and their children\n        for (const view of _data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews){\n            if (view.id === viewId) {\n                targetView = view;\n                break;\n            }\n            if (view.children) {\n                const childView = view.children.find((child)=>child.id === viewId);\n                if (childView) {\n                    targetView = childView;\n                    break;\n                }\n            }\n        }\n        if (targetView && data.name) {\n            targetView.name = data.name;\n            const result = {\n                id: targetView.id,\n                success: true\n            };\n            return simulateApiDelay(result);\n        }\n        return simulateApiDelay({\n            id: viewId,\n            success: false\n        });\n    }\n    /**\n   * Delete an analysis view (mock)\n   */ async deleteAnalysisView(viewId) {\n        // Find and remove the view from our mock data\n        let deleted = false;\n        // Search through parent views\n        for(let i = 0; i < _data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews.length; i++){\n            if (_data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews[i].id === viewId) {\n                _data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews.splice(i, 1);\n                deleted = true;\n                break;\n            }\n            // Search through children\n            if (_data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews[i].children) {\n                const children = _data_mockData__WEBPACK_IMPORTED_MODULE_1__.analysisViews[i].children;\n                if (children) {\n                    const childIndex = children.findIndex((child)=>child.id === viewId);\n                    if (childIndex !== -1) {\n                        children.splice(childIndex, 1);\n                        deleted = true;\n                        break;\n                    }\n                }\n            }\n        }\n        if (deleted) {\n            const result = {\n                success: true,\n                message: 'Analysis view has been deleted'\n            };\n            return simulateApiDelay(result);\n        }\n        return simulateApiDelay({\n            success: false,\n            message: 'Failed to delete analysis view'\n        });\n    }\n}\n// Create and export a singleton instance\nconst mockService = new MockService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/mockService.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/form-data","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fteam%2Fpage&page=%2Fteam%2Fpage&appPaths=%2Fteam%2Fpage&pagePath=private-next-app-dir%2Fteam%2Fpage.tsx&appDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fsheng%2FDeveloper%2FReluvate%2Foneflow%2Fleviathan&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();